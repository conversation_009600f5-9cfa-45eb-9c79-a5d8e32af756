/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220715  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.ecrsdk.message.response;

public class CardPromoptionSaleResponse extends Response {
    private String currencyCode;
    private String exchangeRate;
    private String amtInForeignCurrency;

    @Override
    protected void dataPack() {
        StringBuilder rspData = new StringBuilder();

        rspData.append(new String(new byte[]{getMsgType()}))
                .append(getRspCode())
                .append(getRspMsg())
                .append(new String(new byte[]{getOriTransType()}))
                .append(getEcrReferenceNo())
                .append(getAmountInCents())
                .append(getTipInCents())
                .append(getTransDateTime())
                .append(getCardType())
                .append(getCardNo())
                .append(getExpireDate())
                .append(getTerminalId())
                .append(getMerchantId())
                .append(getTraceNo())
                .append(getBatchNo())
                .append(getApprovalCode())
                .append(getRetrievalRefNo())
                .append(getCurrencyCode())
                .append(getExchangeRate())
                .append(getAmtInForeignCurrency())
                .append(getEntryMode());

        data = rspData.toString().getBytes();
        if (data != null && data.length != 0) {
            //249
            dataLen = toByteArray(String.valueOf(data.length));
        }
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(String exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getAmtInForeignCurrency() {
        return amtInForeignCurrency;
    }

    public void setAmtInForeignCurrency(String amtInForeignCurrency) {
        this.amtInForeignCurrency = amtInForeignCurrency;
    }

}
