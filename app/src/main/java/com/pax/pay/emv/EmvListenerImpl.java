/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.ConditionVariable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.TrackUtils;
import com.pax.commonlib.LogUtils;
import com.pax.edc.R;
import com.pax.edc.opensdk.BaseRequest;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.EmvImpl;
import com.pax.eemv.IEmv;
import com.pax.eemv.IEmvListener;
import com.pax.eemv.entity.Amounts;
import com.pax.eemv.entity.CandList;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.enums.EOnlineResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.eventbus.SearchCardEvent;
import com.pax.jemv.clcommon.RetCode;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.Issuer;
import com.pax.pay.constant.Constants;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.action.ActionEnterAuthCode;
import com.pax.pay.trans.action.ActionEnterInfo;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.pax.pay.utils.Utils.getString;

public class EmvListenerImpl extends EmvBaseListenerImpl implements IEmvListener {

    private static final String TAG = EmvListenerImpl.class.getSimpleName();
    private IEmv emv;

    private final AAction currentAction = TransContext.getInstance().getCurrentAction();

    /**
     * constructor
     *
     * @param context   context
     * @param emv       emv
     * @param transData transData
     * @param listener  listener
     */
    public EmvListenerImpl(Context context, IEmv emv, TransData transData,
                           TransProcessListener listener) {
        super(context, emv, transData, listener);
        this.emv = emv;
    }

    /**
     * onCardHolderPwd
     *
     * @param isOnlinePin         isOnlinePin
     * @param offlinePinLeftTimes offlinePinLeftTimes
     * @param pinData             pinData
     * @return int
     */
    @Override
    public final int onCardHolderPwd(final boolean isOnlinePin, final int offlinePinLeftTimes,
                                     byte[] pinData) {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }
        cv = new ConditionVariable();
        intResult = 0;

        if (pinData != null && pinData[0] != 0) {
            if (pinData[0] == 1) {
                LogUtils.e(TAG, "enter pin timeout");
                return RetCode.EMV_TIME_OUT;
            } else {
                return pinData[0];
            }
        }

        enterPin(isOnlinePin, offlinePinLeftTimes);

        cv.block(); // for the Offline pin case, block it for make sure the PIN activity is ready, otherwise, may get the black screen.
        return intResult;
    }

    @Override
    public final boolean onChkExceptionFile() {
        Log.e(TAG, "onChkExceptionFile");
        byte[] track2 = emv.getTlv(TagsTable.TRACK2);
        String strTrack2 = TrackUtils.getTrack2FromTag57(track2);
        // 卡号
        String pan = TrackUtils.getPan(strTrack2);
        boolean ret = FinancialApplication.getCardBinDb().isBlack(pan);
        if (ret) {
            transProcessListener.onShowErrMessage(
                    context.getString(R.string.emv_card_in_black_list),
                    Constants.FAILED_DIALOG_SHOW_TIME, true);
            return true;
        }
        return false;
    }

    /**
     * onConfirmCardNo
     *
     * @param cardno pan
     * @return int ret
     */
    @Override
    public final int onConfirmCardNo(final String cardno) {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }

        Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(cardno);
        if (issuer == null) {
            intResult = EEmvExceptions.EMV_ERR_DATA.getErrCodeFromBasement();
            return intResult;
        }

        transData.setIssuer(issuer);
        transData.setPinFree(issuer.isRequirePIN());

        String issuerName = transData.getIssuer().getName();
        ETransType transType = transData.getTransType();
        boolean isCupCard = "HASE_CUP".equals(issuerName) || "UnionPay".equals(issuerName);
        boolean isFromDccPreauthMenu = transData.isFromDCCPreauthMenu();
        if (transType == ETransType.PREAUTH) {
            if (isFromDccPreauthMenu && isCupCard) {
                //DCC PREAUTH接口進行的PREAUTH交易,只接受V/M/J/A的卡,不接受银联卡
                intResult = EEmvExceptions.EMV_UNSUPPORTED_CARD.getErrCodeFromBasement();
                return intResult;
            }
            if (!isFromDccPreauthMenu && !isCupCard) {
                //银联预授权菜單內進行的交易只接受以銀聯卡進行的交易,需要拒絕其他卡類的交易
                intResult = EEmvExceptions.EMV_UNSUPPORTED_CARD.getErrCodeFromBasement();
                return intResult;
            }
        }

        if (transType == ETransType.PREAUTHVOID && !isCupCard) {
            //银联预授权撤销只接受银联卡,不接受V/M/J/A的卡
            intResult = EEmvExceptions.EMV_UNSUPPORTED_CARD.getErrCodeFromBasement();
            return intResult;
        }

        if (transType == ETransType.PREAUTHCM) {
            if (!isFromDccPreauthMenu && !isCupCard) {
                //DCC预授权完成不支持插卡
                //银联预授权菜單內進行的银联预授权完成交易只接受以銀聯卡進行的交易,需要拒絕其他卡類的交易
                intResult = EEmvExceptions.EMV_UNSUPPORTED_CARD.getErrCodeFromBasement();
                return intResult;
            }
        }

        findAcqByPanInApp();
        //Intended Offline模式下不允许插卡
        if (FinancialApplication.getBaseManager().isIntendedOfflineMode() && transData.getAcquirer() != null
                && "HASE_EMV".equals(transData.getAcquirer().getName())) {
            intResult = EEmvExceptions.EMV_ERR_FALL_BACK.getErrCodeFromBasement();
            return intResult;
        }
        if (transType == ETransType.AUTHORIZATION) {
            if ("UnionPay".equals(issuerName) || "HASE_CUP".equals(issuerName)) {
                intResult = EEmvExceptions.EMV_UNSUPPORTED_CARD.getErrCodeFromBasement();
                return intResult;
            }
        }

        //ECR发出Loyalty Request Code = 03时，默认进行Cash Dollar
        if (!TextUtils.isEmpty(transData.getEcrLoyaltyCode())) {
            if (transData.isEcrMode() && transData.getEcrLoyaltyCode().equals("03")) {
                //刷卡为非恒生卡返回错误码为TransResult.ECR_NOT_HASE_CARD
                if (!transData.getAcquirer().getName().equals("HASE_OLS")) {
                    intResult = EEmvExceptions.EMV_ERR_NOT_HASE_CARD.getErrCodeFromBasement();
                    return intResult;
                } else {
                    //刷卡为恒生卡直接进入Cash Dollar
                    transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                }
            }

            //当CUP_ECR开关关闭时，沿用旧模式，即ECR下的creadit交易不允许CUP间连交易 ：LoyaltyCode为00时，不接受非恒生CUP卡，若为非恒生CUP卡，返回错误码E02
            if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_CUP_ECR) &&
                    transData.isEcrMode() && transData.getEcrLoyaltyCode().equals("00") && issuerName.equals(
                    "UnionPay")) {
                intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
                return intResult;
            }
        }

        //YUU交易只接受部分恒生VISA card bin
        if (transData.getTransType() == ETransType.YUU_REGISTRATION
                || transData.getTransType() == ETransType.YUU_ENQUIRY) {
            if (!FinancialApplication.getAcqManager().isYUUCardRange(cardno)) {
                intResult = EEmvExceptions.EMV_UNSUPPORTED_CARD.getErrCodeFromBasement();
                return intResult;
            }
        }

        //Normal Cash$ Redeem交易只接受恒生卡
        if (transData.getTransType() == ETransType.SALE && BaseRequest.TRANS_DETAIL_NORMAL_CASH_REDEEM == (transData.getTransTypeDetail())) {
            if (!transData.getAcquirer().getName().equals("HASE_OLS")) {
                intResult = EEmvExceptions.EMV_ERR_NOT_HASE_CARD.getErrCodeFromBasement();
                return intResult;
            }
        }

        cv = new ConditionVariable();

        byte[] holderNameBCD = emv.getTlv(0x5F20);
        if (holderNameBCD == null) {
            holderNameBCD = " ".getBytes();
        }
        String cardHoldName = " ";
        try {
            cardHoldName = new String(holderNameBCD, "GB2312");
        } catch (UnsupportedEncodingException e) {
            Log.e("EmvListenerImp", e.getMessage());
        }
        transData.setCardHolder(cardHoldName);
        byte[] expDateBCD = emv.getTlv(0x5F24);
        String expDate = Utils.bcd2Str(expDateBCD);
        float percent = transData.getTransType().isAdjustAllowed() ? transData.getIssuer().getAdjustPercent()
                : 0; //AET_247
        FinancialApplication.getApp()
                .doEvent(new SearchCardEvent(SearchCardEvent.Status.ICC_UPDATE_CARD_INFO,
                        new CardInfo(cardno, cardHoldName, expDate, percent)));

        if (transData.isEcrMode() && transData.getCardPromotion() != null) {
            String cardPromotion = transData.getCardPromotion();
            if ("07".equals(cardPromotion)) {
                List<String> promotionCardBins = Utils.splitPromotionCardBins(transData.getOtherPromotionCardBin());
                if (!Utils.isCardBinMatched(cardno, promotionCardBins)) {
                    intResult = EEmvExceptions.EMV_ERR_CARD_TYPE_MISMATCH.getErrCodeFromBasement();
                }
            } else {
                if (!Utils.isCardPromotion(issuer, cardPromotion)) {
                    intResult = EEmvExceptions.EMV_ERR_CARD_TYPE_MISMATCH.getErrCodeFromBasement();
                }
            }
            return intResult;
        }

        if (!Component.isDemo() &&
                (!Issuer.validPan(transData.getIssuer(), cardno) ||
                        !Issuer.validCardExpiry(transData.getIssuer(), expDate))) {
            intResult = EEmvExceptions.EMV_ERR_CARD_EXPIRED.getErrCodeFromBasement();
            return intResult;
        }

        FinancialApplication.getApp()
                .doEvent(new SearchCardEvent(SearchCardEvent.Status.ICC_CONFIRM_CARD_NUM,
                        transData));

        cv.block();
        return intResult;
    }

    /**
     * findAcqByPan
     *
     * @param cardno cardno
     * @return String
     */
    @Override
    public final String findIssuerByPan(final String cardno) {
        Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(cardno);
        return issuer != null ? issuer.getName() : "";
    }

    @Override
    public int onInputRefNo() {
        return enterInputRefNo();
    }

    @Override
    public final Amounts onGetAmounts() {
        Amounts amt = new Amounts();
        amt.setTransAmount(String.valueOf(transData.getAmount()));
        return amt;
    }

    @Override
    protected void updateTransDataFromKernel() {
        EmvTransProcess.saveCardInfoAndCardSeq(emv, transData);
        super.updateTransDataFromKernel();
    }

    @Override
    public EOnlineResult onEnquiry() {
        EmvTransProcess.saveCardInfoAndCardSeq(emv, transData);
        int ret = doEnquiryRate(false);
        if (ret != TransResult.SUCC) {
            return EOnlineResult.ABORT;
        }
        if ((transData.getTransType() == ETransType.INC_AUTHORIZATION
                || transData.getTransType() == ETransType.AUTH_REVERSAL) && isRRNReq) {
            ret = enterRRN();
            if (ret != TransResult.SUCC) {
                return EOnlineResult.ABORT;
            }
        }
        if (transData.getTransType() == ETransType.AUTH_REVERSAL && isAuthCodeReq) {
            ret = enterAuthCode();
            if (ret != TransResult.SUCC) {
                return EOnlineResult.ABORT;
            }
        }
        ActivityStack.getInstance().popTo((Activity) context);
        return EOnlineResult.APPROVE;
    }

    private int enterRRN() {
        final ConditionVariable cv = new ConditionVariable();
        ActionEnterInfo enterInfoAction = new ActionEnterInfo(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionEnterInfo) action).setParam(context,
                        transData.getTransType() == ETransType.INC_AUTHORIZATION ? getString(
                                R.string.trans_inc_authorization) : getString(R.string.trans_auth_reversal),
                        transData.isDcc() ? getString(R.string.prompt_dcc_rrn)
                                : getString(R.string.prompt_rrn),
                        transData.isDcc() ? transData.getHomeCurrencyAmount()
                                : String.valueOf(transData.getAmount()),
                        transData.isDcc() ? transData.getHomeCurrency().getCode()
                                : transData.getLocalCurrency().getCode(),
                        12, true, false);
            }
        });
        enterInfoAction.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(currentAction);
                ActivityStack.getInstance().popTo((Activity) context);
                if (result.getRet() != TransResult.SUCC) {
                    intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
                } else {
                    intResult = TransResult.SUCC;
                    String refNo = (String) result.getData();
                    transData.setRefNo(refNo);
                    if (transData.getTransType() == ETransType.INC_AUTHORIZATION) {
                        TransData origTransData =
                                FinancialApplication.getTransDataDbHelper().findTransDataByRefNo(refNo);
                        if (origTransData != null) {
                            transData.setOrigRefNo(refNo);
                            transData.setOrigAuthCode(origTransData.getAuthCode());
                        }
                    }
                }
                cv.open();
            }
        });
        enterInfoAction.execute();
        cv.block();
        return intResult;
    }

    private int enterAuthCode() {
        final ConditionVariable cv = new ConditionVariable();
        ActionEnterAuthCode enterAuthCode = new ActionEnterAuthCode(new AAction.ActionStartListener() {
            public void onStart(AAction action) {
                ((ActionEnterAuthCode) action).setParam(context,
                        getString(R.string.trans_auth_reversal),
                        transData.isDcc() ? getString(R.string.prompt_dcc_auth_code)
                                : getString(R.string.prompt_auth_code),
                        transData.isDcc() ? transData.getHomeCurrencyAmount()
                                : String.valueOf(transData.getAmount()),
                        transData.isDcc() ? transData.getHomeCurrency().getCode()
                                : transData.getLocalCurrency().getCode(),
                        6);
            }
        });
        enterAuthCode.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(currentAction);
                ActivityStack.getInstance().popTo((Activity) context);
                if (result.getRet() != TransResult.SUCC) {
                    intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
                } else {
                    intResult = TransResult.SUCC;
                    String authCode = (String) result.getData();
                    transData.setAuthCode(authCode);
                }
                cv.open();
            }
        });
        enterAuthCode.execute();
        cv.block();
        return intResult;
    }

    @Override
    public void setDccEmvParam() {
        if (!transData.isDcc()) {
            return;
        }
        //set DF55 5F2A, 5F36 for macao set to terminal's local currency, for other currency set to credit card's currency
        try {
            if (!isMacaoLocalCard && !isMacaoTran) {
                emv.setTlv(0x5F2A, Utils.str2Bcd(transData.getHomeCurrency().getCode()));
                emv.setTlv(0x5F36, new byte[]{(byte) transData.getHomeCurrency().getDecimals()});
            } else {
                emv.setTlv(0x5F2A, Utils.str2Bcd(transData.getLocalCurrency().getCode()));
                emv.setTlv(0x5F36, new byte[]{(byte) transData.getLocalCurrency().getDecimals()});
            }
        } catch (EmvException e) {
            Log.e(TAG, e.getErrMsg());
        }
    }

    @Override
    public boolean onSimpleProcessEnd() {
        // 判断仅当为银联预授权完成时才进行Simple Flow的结束
        return ETransType.PREAUTHCM == transData.getTransType() && "UnionPay".equals(transData.getIssuer().getName());
    }

    @Override
    public String getTransAmount() {
        String amount;
        if (!transData.isDcc()) {
            return String.valueOf(transData.getAmount());
        }

        if (!isMacaoLocalCard || !isMacaoTran) {
            amount = transData.getHomeCurrencyAmount();
            if (transData.getHomeCurrency().getCode().equals("360") && FinancialApplication.getAcqManager()
                    .isVisaCard(transData.getIssuer())) {
                amount = amount + "00";
            }
        } else {
            amount = String.valueOf(transData.getAmount());
        }
        return amount;
    }

    @Override
    public EOnlineResult onOnlineProc() {
        return onlineProc();
    }

    @Override
    public final int onWaitAppSelect(final boolean isFirstSelect, final List<CandList> candList) {
        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }
        cv = new ConditionVariable();
        // ignore Sonar's lambda suggestion cuz the Sonar runs JAVA8, but EDC runs JAVA7,
        // there are same cases, ignore them as well.
        FinancialApplication.getApp().runOnUiThread(new SelectAppRunnable(isFirstSelect, candList));

        cv.block();
        return intResult;
    }

    @Override
    public boolean checkCupAcq() {
        return isCupAcq();
    }

    private class SelectAppRunnable implements Runnable {
        private final boolean isFirstSelect;
        private final List<CandList> candList;

        SelectAppRunnable(final boolean isFirstSelect, final List<CandList> candList) {
            this.isFirstSelect = isFirstSelect;
            this.candList = candList;
        }

        @Override
        public void run() {
            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            if (isFirstSelect) {
                builder.setTitle(context.getString(R.string.emv_application_choose));
            } else {
                SpannableString sstr =
                        new SpannableString(context.getString(R.string.emv_application_choose_again));
                sstr.setSpan(new ForegroundColorSpan(Color.RED), 5, 9, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                builder.setTitle(sstr);
            }
            String[] appNames = new String[candList.size()];
            for (int i = 0; i < appNames.length; i++) {
                appNames[i] = candList.get(i).getAppName();
            }
            builder.setSingleChoiceItems(appNames, -1, new DialogInterface.OnClickListener() {

                @Override
                public void onClick(DialogInterface dialog, int which) {
                    intResult = which;
                    close(dialog);
                }
            });

            builder.setPositiveButton(context.getString(R.string.dialog_cancel),
                    new DialogInterface.OnClickListener() {

                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
                            close(dialog);
                        }
                    });
            builder.setCancelable(false);
            builder.create().show();
        }

        private void close(DialogInterface dialog) {
            dialog.dismiss();
            cv.open();
        }
    }

    public void offlinePinEnterReady() {
        cv.open();
    }

    public void cardNumConfigErr() {
        intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
        cv.open();
    }

    public void cardNumConfigSucc() {
        intResult = EEmvExceptions.EMV_OK.getErrCodeFromBasement();
        if (cv != null) {
            cv.open();
        }
    }

    public void cardNumConfigSucc(String[] amount) {
        if (amount != null && amount.length == 2) {
            transData.setAmount(CurrencyConverter.parse(amount[0]));
            transData.setTipAmount(CurrencyConverter.parse(amount[1]));
        }
        cardNumConfigSucc();
    }

    /**
     * set timeout action
     */
    public void onTimeOut() {
        intResult = EEmvExceptions.EMV_ERR_TIMEOUT.getErrCodeFromBasement();
        EmvImpl.isTimeOut = true;
        cv.open();
    }

    public static class CardInfo {
        private String cardNum;
        private String holderName;
        private String expDate;
        private float adjustPercent;

        CardInfo(String cardNum, String holderName, String expDate, float adjustPercent) {
            this.cardNum = cardNum;
            this.holderName = holderName;
            this.expDate = expDate;
            this.adjustPercent = adjustPercent;
        }

        public String getCardNum() {
            return cardNum;
        }

        public String getHolderName() {
            return holderName;
        }

        public String getExpDate() {
            return expDate;
        }

        public float getAdjustPercent() {
            return adjustPercent;
        }
    }

    public final int findAcqByPanInApp() {
        //强制走HASE_CUP后台的Dual Brand卡已强制将acq设置为HASE_CUP，避免重复设置
        if (transData.getAcquirer() != null &&
                !(transData.getAcquirer().getName().equals("HASE_DCC")
                        || (transData.getAcquirer().getName().equals("HASE_MACAO")))) {
            return 0;
        }
        ETransType transType = transData.getTransType();
        //1.如果是CUP PreAuth等交易, 全都送到HASE_CUP后台
        // 2.如果是AMEX卡, 除非走CUP PREAUTH通道, 全部送到AMEX后台
        // 3.如果是AUTHORIZATION或者OFFLINE_SEND交易, 除去前两种情况, 都送到EMV后台
        if (transType == ETransType.PREAUTH
                || transType == ETransType.PREAUTHVOID
                || transType == ETransType.PREAUTHCM) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_CUP");
            transData.setAcquirer(acquirer);
        } else if (Component.isAmex(transData.getIssuer().getName())) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("AMEX");
            transData.setAcquirer(acquirer);
        } else if (transType == ETransType.AUTHORIZATION || transType == ETransType.OFFLINE_TRANS_SEND) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
            transData.setAcquirer(acquirer);
        } else if (transType == ETransType.YUU_REGISTRATION || transType == ETransType.YUU_ENQUIRY) {
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_YUU");
            transData.setAcquirer(acquirer);
        } else {
            Acquirer acquirer =
                    FinancialApplication.getAcqManager().findAcquirer(transData.getIssuer(), transType);
            //如果是VISA/MASTER/JCB，Intended Offline模式下统一归入HASE_EMV收单行
            if (FinancialApplication.getBaseManager().isIntendedOfflineMode() && acquirer != null && "HASE_OLS".equals(acquirer.getName())) {
                acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
            }
            transData.setAcquirer(acquirer);
        }
        return 0;
    }
}
