/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.emv.clss;

import android.app.Activity;
import android.content.Context;
import android.os.ConditionVariable;
import android.text.TextUtils;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.TrackUtils;
import com.pax.dal.ICardReaderHelper;
import com.pax.dal.entity.EReaderType;
import com.pax.dal.entity.PollingResult;
import com.pax.device.Device;
import com.pax.device.DeviceImplNeptune;
import com.pax.edc.R;
import com.pax.edc.opensdk.BaseRequest;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.IClss;
import com.pax.eemv.IClssListener;
import com.pax.eemv.clss.ClssImpl;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.entity.TagsTable;
import com.pax.eemv.enums.ECvmResult;
import com.pax.eemv.enums.EOnlineResult;
import com.pax.eemv.exception.EEmvExceptions;
import com.pax.eemv.exception.EmvException;
import com.pax.eventbus.SearchCardEvent;
import com.pax.jemv.clcommon.RetCode;
import com.pax.jemv.device.model.ApduRespL2;
import com.pax.jemv.device.model.ApduSendL2;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.Issuer;
import com.pax.pay.emv.EmvBaseListenerImpl;
import com.pax.pay.trans.action.ActionAdjustTip;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.transmit.TransProcessListener;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;

import static com.pax.edc.opensdk.TransResult.ERR_USER_CANCEL;

import java.util.List;

/**
 * The type Clss listener.
 */
public class ClssListenerImpl extends EmvBaseListenerImpl implements IClssListener {
    private CTransResult result;
    private static final String TAG = "ClssListenerImpl";
    private IClss clss;

    private static final String MASTER_MCHIP = "A0000000041010";
    private static final String MASTER_MAESTRO = "A0000000043060";

    private boolean detect2ndTap = false;

    private boolean readCardResult = true;
    private int transactionPath;

    /**
     * Instantiates a new Clss listener.
     *
     * @param context   the context
     * @param clss      the clss
     * @param transData the trans data
     * @param listener  the listener
     */
    public ClssListenerImpl(Context context, IClss clss, TransData transData,
                            TransProcessListener listener) {
        super(context, clss, transData, listener);
        this.clss = clss;
    }

    /**
     * onCvmResult
     *
     * @param result ECvmResult
     * @return int ret
     */
    @Override
    public int onCvmResult(ECvmResult result) {
        if (!readCardResult) {
            return EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
        }

        if (transProcessListener != null) {
            transProcessListener.onHideProgress();
        }
        intResult = 0;

        if (result == ECvmResult.ONLINE_PIN || result == ECvmResult.ONLINE_PIN_SIG) {
            cv = new ConditionVariable();
            enterPin(true, 0);
            cv.block(); // for the Offline pin case, block it for make sure the PIN activity is ready, otherwise, may get the black screen.
        }

        // TODO for the case of e-sign, it requires upload e-sign 8583 message which is not supported by host for now, so we ignore it and do signature on receipt.

        return intResult;
    }

    /**
     * @throws EmvException EmvException
     */
    @Override
    public void onComfirmCardInfo(String track1, String track2, String track3) throws EmvException {
        transData.setTrack1(track1);
        transData.setTrack2(track2);
        transData.setTrack3(track3);

        String pan = TrackUtils.getPan(track2);
        transData.setPan(pan);
        Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(pan);
        if (transData.isEcrMode() && transData.getCardPromotion() != null) {
            String cardPromotion = transData.getCardPromotion();
            if ("07".equals(cardPromotion)) {
                List<String> promotionCardBins = Utils.splitPromotionCardBins(transData.getOtherPromotionCardBin());
                if (!Utils.isCardBinMatched(pan, promotionCardBins)) {
                    throw new EmvException(EEmvExceptions.EMV_ERR_CARD_TYPE_MISMATCH);
                }
            } else {
                if (!Utils.isCardPromotion(issuer, cardPromotion)) {
                    throw new EmvException(EEmvExceptions.EMV_ERR_CARD_TYPE_MISMATCH);
                }
            }
        }
        if (issuer != null) {
            transData.setIssuer(issuer);
            transData.setPinFree(!issuer.isRequirePIN());
            transData.setTransactionPath(getTransactionPath());
            ETransType transType = transData.getTransType();

            if (transData.getAcquirer() == null
                    || transData.getAcquirer().getName().equals("HASE_DCC")
                    || transData.getAcquirer().getName().equals("HASE_MACAO")) {
                //1.如果是PreAuth等交易, 全都送到HASE_CUP后台
                // 2.如果是AMEX卡, 除非走CUP PREAUTH通道, 全部送到AMEX后台
                // 3.如果是AUTHORIZATION或者OFFLINE_SEND交易, 除去前两种情况, 都送到EMV后台
                if (transType == ETransType.PREAUTH || transType == ETransType.PREAUTHVOID || transType == ETransType.PREAUTHCM) {
                    Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_CUP");
                    transData.setAcquirer(acquirer);
                } else if (Component.isAmex(issuer.getName())) {
                    Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("AMEX");
                    transData.setAcquirer(acquirer);
                } else if (transType == ETransType.AUTHORIZATION || transType == ETransType.OFFLINE_TRANS_SEND) {
                    Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
                    transData.setAcquirer(acquirer);
                } else if (transType == ETransType.YUU_REGISTRATION || transType == ETransType.YUU_ENQUIRY) {
                    Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_YUU");
                    transData.setAcquirer(acquirer);
                } else {
                    Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(issuer, transType);
                    //如果是VISA/MASTER/JCB，Intended Offline模式下统一归入HASE_EMV收单行
                    if (FinancialApplication.getBaseManager().isIntendedOfflineMode() && acquirer != null && "HASE_OLS".equals(acquirer.getName())) {
                        acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
                    }
                    transData.setAcquirer(acquirer);
                }
            }
        } else {
            throw new EmvException(EEmvExceptions.EMV_ERR_DATA);
        }
        if ("HASE_EMV".equals(transData.getAcquirer().getName())) {
            ClssImpl.isEMVAcquirer = true;
        } else {
            ClssImpl.isEMVAcquirer = false;
        }
        String expDate = TrackUtils.getExpDate(transData.getTrack2());
        transData.setExpDate(expDate);
        if (!Component.isDemo() &&
                (!Issuer.validPan(transData.getIssuer(), pan) ||
                        !Issuer.validCardExpiry(transData.getIssuer(), expDate))) {
            throw new EmvException(EEmvExceptions.EMV_ERR_CLSS_CARD_EXPIRED);
        }

        //PanSeqNo
        byte[] value = clss.getTlv(TagsTable.PAN_SEQ_NO);
        if (value != null) {
            String cardSerialNo = Utils.bcd2Str(value);
            transData.setCardSerialNo(cardSerialNo.substring(0, value.length * 2));
        }

        if (!TextUtils.isEmpty(transData.getEcrLoyaltyCode())) {
            //ECR发出Loyalty Request Code = 03时，默认进行Cash Dollar
            if (transData.isEcrMode() && transData.getEcrLoyaltyCode().equals("03")) {
                //刷卡为非恒生卡返回错误码为TransResult.ECR_NOT_HASE_CARD
                if (!transData.getAcquirer().getName().equals("HASE_OLS")) {
                    throw new EmvException(EEmvExceptions.EMV_ERR_NOT_HASE_CARD);
                } else {
                    //刷卡为恒生卡直接进入Cash Dollar
                    transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                }
            }

            //当CUP_ECR开关关闭时，沿用旧模式，即ECR下的creadit交易不允许CUP间连交易 ：LoyaltyCode为00时不接受非恒生CUP卡，若为非恒生CUP卡，返回错误码E02
            if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_CUP_ECR) &&
                    transData.isEcrMode() && transData.getEcrLoyaltyCode().equals("00") && transData.getIssuer().getName().equals("UnionPay")) {
                throw new EmvException(EEmvExceptions.EMV_ERR_USER_CANCEL);
            }
        }

        //YUU交易只接受部分恒生VISA card bin
        if (transData.getTransType() == ETransType.YUU_REGISTRATION || transData.getTransType() == ETransType.YUU_ENQUIRY) {
            if (!FinancialApplication.getAcqManager().isYUUCardRange(pan)) {
                throw new EmvException(EEmvExceptions.EMV_ERR_CLSS_TRY_ANOTHER_CARD);
            }
        }

        //Normal Cash$ Redeem交易只接受恒生卡
        if (transData.getTransType() == ETransType.SALE && BaseRequest.TRANS_DETAIL_NORMAL_CASH_REDEEM == (transData.getTransTypeDetail())) {
            if (!transData.getAcquirer().getName().equals("HASE_OLS")) {
                throw new EmvException(EEmvExceptions.EMV_ERR_NOT_HASE_CARD);
            }
        }

        //Jerry add 20180125
        cv = new ConditionVariable();
        FinancialApplication.getApp()
                .doEvent(new SearchCardEvent(SearchCardEvent.Status.PICC_SELECT_CASH_DOL,
                        transData));
        cv.block();
    }

    /**
     * Read card success.
     */
    public void readCardSuccess() {
        readCardResult = true;
        cv.open();
    }

    /**
     * Read card fail.
     */
    public void readCardFail() {
        readCardResult = false;
        cv.open();
    }

    @Override
    protected void updateTransDataFromKernel() {
        ClssTransProcess.clssTransResultProcess(result, clss, transData);
    }

    @Override
    public EOnlineResult onEnquiry() {
        int ret = doEnquiryRate(true);
        if (ret != TransResult.SUCC) {
            return EOnlineResult.ABORT;
        }
        ActivityStack.getInstance().popTo((Activity) context);
        return EOnlineResult.APPROVE;
    }

    @Override
    public String getTransAmount() {
        String amount;
        if (!transData.isDcc()) {
            return String.valueOf(transData.getAmount());
        }
        if (!isMacaoLocalCard) {
            amount = transData.getHomeCurrencyAmount();
            if (transData.getHomeCurrency().getCode().equals("360") && FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer())) {
                amount = amount + "00";
            }
        } else {
            amount = String.valueOf(transData.getAmount());
        }
        return amount;
    }

    @Override
    public EOnlineResult onOnlineProc(CTransResult result) {
        this.result = result;
        return onlineProc();
    }

    @Override
    public boolean onDetect2ndTap() {
        final ConditionVariable cv = new ConditionVariable();
        FinancialApplication.getApp().runInBackground(new Runnable() {
            @Override
            public void run() {
                if (transData.getEnterMode() == TransData.EnterMode.CLSS && transProcessListener != null) {
                    transProcessListener.onShowProgress(context.getString(R.string.prompt_wave_card), 30);
                }
                try {
                    //tap card
                    ICardReaderHelper helper = FinancialApplication.getDal().getCardReaderHelper();
                    helper.polling(EReaderType.PICC, 30 * 1000);
                    helper.stopPolling();
                    detect2ndTap = true;
                } catch (Exception e) {
                    Log.e(TAG, "", e);
                } finally {
                    if (transProcessListener != null)
                        transProcessListener.onHideProgress();
                    cv.open();
                }
            }
        });
        cv.block();
        return detect2ndTap;
    }

    @Override
    public byte[] onUpdateKernelCfg(String aid) {
        if (MASTER_MCHIP.equals(aid)) {
            return new byte[]{(byte) 0x20};
        } else if (MASTER_MAESTRO.equals(aid)) {
            return new byte[]{(byte) 0xA0};
        }
        return null;
    }

    @Override
    public int onIssScrCon() {
        ApduSendL2 apduSendL2 = new ApduSendL2();
        ApduRespL2 apduRespL2 = new ApduRespL2();
        byte[] sendCommand = new byte[]{(byte) 0x00, (byte) 0xA4, (byte) 0x04, (byte) 0x00};
        System.arraycopy(sendCommand, 0, apduSendL2.command, 0, sendCommand.length);
        apduSendL2.lc = 14;
        String sendDataIn = "1PAY.SYS.DDF01";
        System.arraycopy(sendDataIn.getBytes(), 0, apduSendL2.dataIn, 0, sendDataIn.getBytes().length);
        apduSendL2.le = 256;
        int ret = (int) DeviceImplNeptune.getInstance().iccCommand(apduSendL2, apduRespL2);
        if (ret != RetCode.EMV_OK)
            return ret;

        if (apduRespL2.swa != (byte) 0x90 || apduRespL2.swb != 0x00)
            return RetCode.EMV_RSP_ERR;

        apduSendL2 = new ApduSendL2();
        apduRespL2 = new ApduRespL2();
        System.arraycopy(sendCommand, 0, apduSendL2.command, 0, sendCommand.length);
        apduSendL2.lc = 14;
        System.arraycopy(transData.getAid().getBytes(), 0, apduSendL2.dataIn, 0, transData.getAid().getBytes().length);
        apduSendL2.le = 256;
        ret = (int) DeviceImplNeptune.getInstance().iccCommand(apduSendL2, apduRespL2);
        if (ret != RetCode.EMV_OK)
            return ret;

        if (apduRespL2.swa != (byte) 0x90 || apduRespL2.swb != 0x00)
            return RetCode.EMV_RSP_ERR;

        return RetCode.EMV_OK;
    }

    @Override
    public void onPromptRemoveCard() {
        if (!Utils.isAutoTestBuild()) {
            final ConditionVariable cv = new ConditionVariable();
            FinancialApplication.getApp().doEvent(new SearchCardEvent(SearchCardEvent.Status.CLSS_LIGHT_STATUS_REMOVE_CARD));
            FinancialApplication.getApp().runInBackground(new Runnable() {
                @Override
                public void run() {
                    Device.removeCard(new Device.RemoveCardListener() {
                        @Override
                        public void onShowMsg(PollingResult result) {
                            if (transProcessListener != null) {
                                transProcessListener.onShowNormalMessage(context.getString(R.string.wait_remove_card), 1, false);//Raymond asked that timeout=1sec
                            }
                        }
                    });
                    if (transProcessListener != null) {
                        transProcessListener.onHideProgress();
                    }
                    cv.open();
                }
            });
            cv.block();
        }
    }

    @Override
    public int onDisplaySeePhone() {
        FinancialApplication.getApp().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ToastUtils.showMessage("Please See Phone");
            }
        });
        return 0;
    }

    @Override
    public boolean onCheckDemoMode() {
        return Component.isDemo();
    }

    @Override
    public void setTransactionPath(int transactionPath) {
        this.transactionPath = transactionPath;
    }

    @Override
    public int onInputRefNo() {
        if (!readCardResult) {
            return EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
        }
        return enterInputRefNo();
    }

    @Override
    public boolean checkCupAcq() {
        return isCupAcq();
    }

    /**
     * Gets transaction path.
     *
     * @return the transaction path
     */
    public int getTransactionPath() {
        return transactionPath;
    }

    @Override
    public int onInputTip() {
        boolean enableTip =
                FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP)
                        && FinancialApplication.getSysParam()
                        .get(SysParam.BooleanParam.EDC_TIP_TYPE);
        if (transData.getAcquirer().getName().equals("HASE_CUP") || transData.getAcquirer()
                .getName()
                .equals("HASE_OLS")) {
            //Issue 94 Disable the CUP tips as HASE host not yet support
            //恒生卡非接交易不支持小费
            enableTip = false;
        }

        if (!enableTip) {
            return TransResult.SUCC;
        }

        if (!readCardResult) {
            return EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
        }

        final ConditionVariable cv = new ConditionVariable();
        ActionAdjustTip adjustTipAction = new ActionAdjustTip(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                String amount = String.valueOf(transData.getAmount() - transData.getTipAmount());
                float percent = transData.getIssuer().getAdjustPercent();
                //传入小费参数以供显示
                ((ActionAdjustTip) action).setParam(context,
                        transData.getTransType().getTransName(),
                        amount, percent, transData.getTipAmount());
            }
        });
        adjustTipAction.setEndListener(new AAction.ActionEndListener() {
            @Override
            public void onEnd(AAction action, ActionResult result) {
                int ret = result.getRet();
                if (ret == ERR_USER_CANCEL) {
                    intResult = EEmvExceptions.EMV_ERR_USER_CANCEL.getErrCodeFromBasement();
                    cv.open();
                    return;
                }

                transData.setIsPreSaleTips(true);
                //get total amount
                long totalAmountStr = CurrencyConverter.parse(result.getData().toString());
                transData.setAmount(totalAmountStr);
                //get tip amount
                long tip = CurrencyConverter.parse(result.getData1().toString());
                //set tip amount
                transData.setTipAmount(tip);
                intResult = TransResult.SUCC;
                cv.open();
            }
        });
        adjustTipAction.execute();
        cv.block();
        return intResult;
    }
}

