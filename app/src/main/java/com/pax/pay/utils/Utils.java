/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-27
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.PowerManager;
import android.preference.PreferenceManager;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import android.telephony.TelephonyManager;
import android.util.DisplayMetrics;
import android.util.Log;

import com.alibaba.fastjson.JSONReader;
import com.pax.dal.ISys;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.edc.BuildConfig;
import com.pax.edc.R;
import com.pax.glwrapper.convert.IConvert;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Issuer;
import com.pax.pay.menu.IntendedOfflineActivity;
import com.pax.pay.menu.TransMenuActivity;
import com.pax.pay.trans.component.Component;
import com.pax.settings.SysParam;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static android.content.Context.TELEPHONY_SERVICE;
import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

public class Utils {

    private static final String TAG = "Utils";

    private Utils() {
        //do nothing
    }

    /**
     * 获取主秘钥索引
     *
     * @param index 0~99的主秘钥索引值
     * @return 1~100的主秘钥索引值
     */
    public static int getMainKeyIndex(int index) {
        return index + 1;
    }

    public static boolean checkIp(String ip) {
        return ip.matches(
                "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)");
    }

    /**
     *
     */
    private static void changeAppLanguageV5(Context context, Locale locale) {
        Resources res = context.getResources();
        DisplayMetrics dm = res.getDisplayMetrics();
        Configuration conf = res.getConfiguration();
        conf.locale = locale;
        res.updateConfiguration(conf, dm);
    }

    /**
     * @param context
     * @param locale
     * @return
     */
    public static Context changeAppLanguage(Context context, Locale locale) {
        if ("zh_".equals(locale.toString().substring(0, 3))) {
            locale = Locale.CHINA;
            FinancialApplication.getSysParam()
                    .set(SysParam.StringParam.APP_LANGUAGE,
                            Utils.getString(R.string.LANGUAGE_CHINESE));
        } else {
            locale = Locale.US;
            FinancialApplication.getSysParam()
                    .set(SysParam.StringParam.APP_LANGUAGE,
                            Utils.getString(R.string.LANGUAGE_ENGLISH));
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return changeAppLanguageV7(context, locale);
        } else {
            changeAppLanguageV5(context, locale);
            return context;
        }
    }

    /**
     * @param context
     * @return
     */
    public static Context changeAppLanguageInApp(Context context) {
        String language = FinancialApplication.getSysParam().get(SysParam.StringParam.APP_LANGUAGE);
        Locale locale = null;
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        if (language != null) {
            if (language.equals(Utils.getString(R.string.LANGUAGE_ENGLISH))) {
                locale = Locale.US;
            } else if (language.equals(Utils.getString(R.string.LANGUAGE_CHINESE))) {
                locale = Locale.CHINA;
            }
        } else {
            locale = Locale.getDefault();
            if ("zh_".equals(locale.toString().substring(0, 3))) {
                locale = Locale.CHINA;
            } else {
                locale = Locale.US;
            }
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLocale(locale);
        } else {
            configuration.locale = locale;
        }
        DisplayMetrics displayMetrics = resources.getDisplayMetrics();
        resources.updateConfiguration(configuration, displayMetrics);
        return context;
    }

    /**
     *
     */
    @TargetApi(Build.VERSION_CODES.N)
    private static Context changeAppLanguageV7(Context context, Locale locale) {
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        configuration.setLocale(locale);
        return context.createConfigurationContext(configuration);
    }

    /**
     *
     */
    @NonNull
    public static String getString(@StringRes int resId) {
        return FinancialApplication.getApp().getString(resId);
    }

    /**
     *
     */
    @NonNull
    public static String getString(@StringRes int resId, Object... formatArgs) {
        return FinancialApplication.getApp().getResources().getString(resId, formatArgs);
    }

    /**
     *
     */
    public static byte[] str2Bcd(String str) {
        return FinancialApplication.getConvert()
                .strToBcd(str, IConvert.EPaddingPosition.PADDING_LEFT);
    }

    /**
     *
     */
    public static String bcd2Str(byte[] bcd) {
        return FinancialApplication.getConvert().bcdToStr(bcd);
    }

    /**
     * easy way to get permission, for getting more interactions, should show request permission
     * rationale
     */
    public static Disposable callPermission(@NonNull FragmentActivity activity, @NonNull Action action, final String failedMsg, String... permissions) {
        RxPermissions rxPermissions = new RxPermissions(activity); // where this is an Activity instance
        return rxPermissions
                .requestEachCombined(permissions)
                .subscribe(new Consumer<Permission>() {
                    @Override
                    public void accept(Permission permission) throws Exception {
                        if (!permission.granted) {
                            // 未获取权限
                            ToastUtils.showMessage(failedMsg);

                        } else {
                            try {
                                action.run();
                            } catch (Exception e) {
                                Log.e(TAG, "Error running action", e);
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "{accept Throwable}", throwable);
                    }
                });
    }

    /**
     * @param context
     * @return
     */
    public static boolean isSMSAvailable(Context context) {
        TelephonyManager manager = (TelephonyManager) context.getSystemService(TELEPHONY_SERVICE);
        return (manager.getSimState() == TelephonyManager.SIM_STATE_READY) && (
                ContextCompat.checkSelfPermission(context, Manifest.permission.SEND_SMS)
                        != PackageManager.PERMISSION_GRANTED);
    }

    /**
     *
     */
    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager connectivity = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivity != null) {
            NetworkInfo info = connectivity.getActiveNetworkInfo();
            return info != null
                    && info.isConnected()
                    && info.getState() == NetworkInfo.State.CONNECTED;
        }
        return false;
    }

    /**
     *
     */
    public static void wakeupScreen(int timeout) {
        PowerManager pm = (PowerManager) FinancialApplication.getApp()
                .getSystemService(Context.POWER_SERVICE);
        @SuppressLint("InvalidWakeLockTag") final PowerManager.WakeLock wl = pm.newWakeLock(
                PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.SCREEN_BRIGHT_WAKE_LOCK,
                "bright");
        wl.acquire();
        FinancialApplication.getApp().runOnUiThreadDelay(new Runnable() {
            @Override
            public void run() {
                wl.release();
            }
        }, 1000L * (timeout + 1));
    }

    public static void restart() {
        ActivityStack.getInstance().popAll();
        Intent intent = new Intent();
        intent.addFlags(FLAG_ACTIVITY_NEW_TASK);
        intent.setClass(FinancialApplication.getApp(), TransMenuActivity.class);
        FinancialApplication.getApp().startActivity(intent);
    }

    public static void recreateIntendedActivity() {
        ActivityStack.getInstance().pop();
        Intent intent = new Intent();
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.setClass(FinancialApplication.getApp(), IntendedOfflineActivity.class);
        FinancialApplication.getApp().startActivity(intent);
    }

    public static boolean isAutoTestBuild() {
        return "autoTest".equals(BuildConfig.FLAVOR);
    }

    /**
     * @param context
     * @param action
     */
    public static void callSystemSettings(Context context, String action) {
        context.startActivity(new Intent(action));
    }

    /**
     *
     */
    public static <T> List<T> readObjFromJSON(String fileName, Class<T> clz) {
        List<T> list = new ArrayList<>();
        try (InputStreamReader reader = new InputStreamReader(
                FinancialApplication.getApp().getAssets().open(fileName))) {
            JSONReader jsonReader = new JSONReader(reader);
            jsonReader.startArray();
            while (jsonReader.hasNext()) {
                T obj = jsonReader.readObject(clz);
                list.add(obj);
            }
            jsonReader.endArray();
            jsonReader.close();
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }
        return list;
    }

    /**
     * @param longStr
     * @param safeValue
     * @return
     */
    public static long parseLongSafe(String longStr, long safeValue) {
        if (longStr == null) {
            return safeValue;
        }
        try {
            return Long.parseLong(longStr);
        } catch (NumberFormatException e) {
            return safeValue;
        }
    }

    /**
     * Parse int safe int.
     *
     * @param intStr the int str
     * @return the int
     */
    public static int parseIntSafe(String intStr) {
        return parseIntSafe(intStr, 0);
    }

    /**
     * Parse int safe int.
     *
     * @param intStr       the int str
     * @param defaultValue the default value
     * @return the int
     */
    public static int parseIntSafe(String intStr, int defaultValue) {
        try {
            return Integer.parseInt(intStr);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 如果应用的私有数据区Files文件夹中没有, 就从Assets中找
     *
     * @param fileName 文件名
     * @return 图像的bitmap
     */
    public static Bitmap getImageFile(String fileName) {
        Bitmap image = null;
        InputStream is;
        try {
            if (isFileExist(Component.RECEIPT_LOGO)) {
                is = FinancialApplication.getApp().openFileInput(Component.RECEIPT_LOGO);
            } else {
                AssetManager am = FinancialApplication.getApp().getResources().getAssets();
                is = am.open(fileName);
            }

            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }

        return image;
    }

    public static String inverseRate(String rate, int scale) {
        if (rate == null || rate.isEmpty()) {
            return "";
        }
        String temp = rate.substring(1);
        int decimalLength = rate.charAt(0) - '0';
        String integer = temp.length() - decimalLength == 0 ? "0" : temp.substring(0, temp.length() - decimalLength);
        String decimal = temp.substring(temp.length() - decimalLength);

        BigDecimal one = new BigDecimal("1");
        BigDecimal b = new BigDecimal(integer + "." + decimal);
        BigDecimal rateDouble = one.divide(b, 20, BigDecimal.ROUND_HALF_UP);

        return String.valueOf(rateDouble).substring(0, scale);
    }

    public static String initRate(String rate) {
        if (rate == null || rate.isEmpty()) {
            return "";
        }
        String temp = rate.substring(1);
        int decimalLength = rate.charAt(0) - '0';
        String integer = temp.length() - decimalLength == 0 ? "0" : temp.substring(0, temp.length() - decimalLength);
        String decimal = temp.substring(temp.length() - decimalLength);
        Double rateDouble = Double.parseDouble(integer + "." + decimal);
        String initRate = String.valueOf(rateDouble);
        if (initRate.length() < 8) {
            initRate = initRate + "00000000".substring(0, initRate.length());
        }
        return initRate.substring(0, 8);
    }

    public static String initMarkupRate(String markupRate) {
        if (markupRate == null || markupRate.isEmpty()) {
            return "";
        }
        return markupRate.substring(0, 2).replace("0", "") + "." + markupRate.substring(2, 4) + "%";
    }

    public static String readSN() {
        ISys iSys = FinancialApplication.getDal().getSys();
        Map<ETermInfoKey, String> map = iSys.getTermInfo();
        return map.get(ETermInfoKey.SN);
    }

    public static Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            Log.e(TAG, "", e);
        }

        return image;
    }

    public static boolean checkAcquirerSettleStatus(String acquirer) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences
                (FinancialApplication.getApp());
        switch (acquirer) {
            case "AMEX":
                return preferences.getBoolean(SysParam.BooleanParam.AMEX_SETTLE_STATUS.toString(),
                        true);
            case "HASE_OLS":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_OLS_SETTLE_STATUS.toString(), true);
            case "HASE":
                return preferences.getBoolean(SysParam.BooleanParam.HASE_SETTLE_STATUS.toString(),
                        true);
            case "HASE_EMV":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_EMV_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P1":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP1_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P2":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP2_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P3":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP3_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P4":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP4_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P5":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP5_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P6":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP6_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P7":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP7_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P8":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP8_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P9":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP9_SETTLE_STATUS.toString(), true);
            case "HASE_INS_P10":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_INSP10_SETTLE_STATUS.toString(), true);
            case "HASE_CUP":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_CUP_SETTLE_STATUS.toString(), true);
            case "HASE_DCC":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_DCC_SETTLE_STATUS.toString(), true);
            case "HASE_MACAO":
                return preferences.getBoolean(
                        SysParam.BooleanParam.HASE_MACAO_SETTLE_STATUS.toString(), true);
            default:
                return true;
        }
    }

    /**
     * @param bytes
     * @param start
     * @param end
     * @return
     */
    public static byte[] subBytes(byte[] bytes, int start, int end) {
        byte[] subBytes = new byte[end - start];
        System.arraycopy(bytes, start, subBytes, 0, end - start);
        return subBytes;
    }

    /**
     * @param fileName Cert path
     * @return Is cert exist or not
     */
    public static boolean isFileExist(String fileName) {
        return new File(FinancialApplication.getApp().getFilesDir().getAbsolutePath()
                + File.separator
                + fileName).exists();
    }

    //判断asserts文件夹下是否存在某个文件
    public static boolean isFileExistInAsserts(String fileName) {
        AssetManager am = FinancialApplication.getApp().getResources().getAssets();
        try {
            String[] names = am.list("");
            for (int i = 0; i < names.length; i++) {
                if (names[i].equals(fileName)) {
                    return true;
                }
            }
        } catch (IOException e) {
            Log.e(TAG, "", e);
            return false;
        }
        return false;
    }

    /**
     *
     */
    public static String getWifiSSID(Context context) {
        WifiManager wifiMgr = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiInfo info = wifiMgr.getConnectionInfo();
        return info != null ? info.getSSID().replace("\"", "") : null;
    }

    /**
     *
     */
    public static boolean isWifiEnabled(Context context) {
        WifiManager wifiMgr = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        if (wifiMgr.getWifiState() == WifiManager.WIFI_STATE_ENABLED) {
            ConnectivityManager connManager = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo wifiInfo = connManager
                    .getNetworkInfo(ConnectivityManager.TYPE_WIFI);
            return wifiInfo.isConnected();
        } else {
            return false;
        }
    }

    /**
     *
     */
    public static byte[] byteMerger(byte[] paramArrayOfByte1, byte[] paramArrayOfByte2) {
        byte[] arrayOfByte = new byte[paramArrayOfByte1.length + paramArrayOfByte2.length];
        System.arraycopy(paramArrayOfByte1, 0, arrayOfByte, 0, paramArrayOfByte1.length);
        System.arraycopy(paramArrayOfByte2, 0, arrayOfByte, paramArrayOfByte1.length,
                paramArrayOfByte2.length);
        return arrayOfByte;
    }

    /**
     * @param context
     * @param packageName
     * @return
     */
    public static boolean isAppInstalled(Context context, String packageName) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "", e);
            return false;
        }
        return packageInfo != null;
    }

    /**
     * change pan digit positon
     */
    public static String panShuffle(String pan, int panLength, int panShift) {
        int panDigit;
        int[] panBuf = new int[panLength];
        StringBuilder panShuffle = new StringBuilder();
        for (int i = 6; i < 6 + panLength; i++) {
            panBuf[i - 6] = Integer.parseInt(pan.substring(i, i + 1));
        }
        //transfer cardno
        for (int i = 0; i < panBuf.length; i++) {
            panBuf[i] = panMap(panBuf[i]);
        }
        //right_shift offset
        for (int j = 0; j < panShift; j++) {
            panDigit = panBuf[panLength - 1];
            for (int i = panLength - 1; i > 0; i--) {
                panBuf[i] = panBuf[i - 1];
            }
            panBuf[0] = panDigit;
        }
        for (int i = 0; i < panBuf.length; i++) {
            panShuffle.append(panBuf[i]);
        }
        StringBuilder replace = new StringBuilder(pan);
        replace.replace(6, 6 + panLength, String.valueOf(panShuffle));

        return String.valueOf(replace);
    }

    /**
     * change panDigit
     *
     * @param PanDigit panNo
     */
    private static int panMap(int PanDigit) {
        int mapDigit = 0;
        switch (PanDigit) {
            case 0:
                mapDigit = 3;
                break;
            case 1:
                mapDigit = 5;
                break;
            case 2:
                mapDigit = 7;
                break;
            case 3:
                mapDigit = 9;
                break;
            case 4:
                mapDigit = 1;
                break;
            case 5:
                mapDigit = 2;
                break;
            case 6:
                mapDigit = 8;
                break;
            case 7:
                mapDigit = 4;
                break;
            case 8:
                mapDigit = 6;
                break;
            case 9:
                mapDigit = 0;
                break;
            default:
                break;
        }
        return mapDigit;
    }

    /**
     * 获取货币Symbol, 如 HK$, ¥
     */
    public static String getCurrencySymbol() {
        String formatAmount = CurrencyConverter.convert(0L, CurrencyConverter.getDefCurrency());
        return formatAmount.substring(0, formatAmount.indexOf('0'));
    }

    /**
     * @param decimalNumber 将Int数字转换成0x开头的十六进制
     * @return String
     */
    public static String decimalToHexString(int decimalNumber) {
        //bug-10871 by fanjiaming 2023/9/14 start
        //只有1以上的数字才是有效数字
        if (decimalNumber <= 0) return null;
        //bug-10871 by fanjiaming 2023/9/14 end
        String hexString = Integer.toHexString(decimalNumber).toUpperCase();
        //长度不够则左补零
        if (hexString.length() < 2)
            hexString = "0" + hexString;
        return "0x" + hexString;
    }

    /**
     * @param hexString 将0x开头的十六进制转成数字
     * @return int
     */
    public static int hexStringToDecimal(String hexString) {
        if (hexString == null || hexString.length() <= 2)
            return 0;
        else
            return Integer.parseInt(hexString.substring(2), 16);
    }

    public static boolean isA920Pro() {
        return Build.MODEL.equalsIgnoreCase("A920Pro");
    }

    /**
     * 将输入字符串全部替换为星号(*)，保留原始长度
     *
     * @param value 要遮蔽的字段值（如卡号、有效期）
     * @return 与原字符串长度相同的由 * 组成的字符串
     */
    public static String maskAll(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        return "*".repeat(value.length());
    }

    public static boolean isCardPromotion(Issuer issuer, String cardPromotion) {
        String issuerName = issuer.getName();
        switch (cardPromotion) {
            case "01": // ENJOY card only
                return issuerName.equals(getString(R.string.issuer_hase_enj))
                        || issuerName.equals(getString(R.string.issuer_hase_enj_e));

            case "02": // HASE VISA card (excluding ENJOY card)
                return issuerName.equals(getString(R.string.issuer_hase_visa));

            case "03": // All HASE VISA cards (including ENJOY card)
                return issuerName.equals(getString(R.string.issuer_hase_enj))
                        || issuerName.equals(getString(R.string.issuer_hase_enj_e))
                        || issuerName.equals(getString(R.string.issuer_hase_visa));

            case "04": // HASE MASTERCARD card
                return issuerName.equals(getString(R.string.issuer_hase_master));

            case "05": // HASE CUP card
                return issuerName.equals(getString(R.string.issuer_hase_cup));

            case "06": // All HASE cards (HASE VISA including ENJOY, HASE MASTERCARD, and HASE CUP)
                return issuerName.equals(getString(R.string.issuer_hase_enj))
                        || issuerName.equals(getString(R.string.issuer_hase_enj_e))
                        || issuerName.equals(getString(R.string.issuer_hase_visa))
                        || issuerName.equals(getString(R.string.issuer_hase_master))
                        || issuerName.equals(getString(R.string.issuer_hase_cup));
            default:
                return false;
        }
    }

    public static boolean isCardBinMatched(String cardNumber, List<String> promotionCardBins) {
        if (cardNumber == null || cardNumber.isEmpty()) {
            return false;
        }
        for (String bin : promotionCardBins) {
            if (bin != null && !bin.isEmpty() && cardNumber.length() >= bin.length()) {
                String cardPrefix = cardNumber.substring(0, bin.length());
                if (cardPrefix.equals(bin)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static List<String> splitPromotionCardBins(String otherPromotionCardBin) {
        List<String> promotionCardBins = new ArrayList<>();
        if (otherPromotionCardBin == null) {
            return promotionCardBins;
        }
        for (int i = 0; i < 10; i++) {
            int start = i * 10;
            int end = Math.min(start + 10, otherPromotionCardBin.length());
            if (start >= otherPromotionCardBin.length()) {
                break;
            }
            String bin = otherPromotionCardBin.substring(start, end).trim();
            if (!bin.isEmpty()) {
                promotionCardBins.add(bin);
            }
        }
        return promotionCardBins;
    }
}
