/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Sim.G
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans.action.activity;

import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;

import androidx.annotation.NonNull;

import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.pax.abl.core.AAction;
import com.pax.abl.core.ActionResult;
import com.pax.abl.utils.EncUtils;
import com.pax.abl.utils.PanUtils;
import com.pax.abl.utils.TrackUtils;
import com.pax.dal.ICardReaderHelper;
import com.pax.dal.entity.EReaderType;
import com.pax.dal.entity.PollingResult;
import com.pax.dal.exceptions.IccDevException;
import com.pax.dal.exceptions.MagDevException;
import com.pax.dal.exceptions.PiccDevException;
import com.pax.device.Device;
import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.eventbus.ClssCallbackEvent;
import com.pax.eventbus.EmvCallbackEvent;
import com.pax.eventbus.Event;
import com.pax.eventbus.SearchCardEvent;
import com.pax.eventbus.SettlePendingEvent;
import com.pax.pay.BaseActivityWithTickForAction;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.Issuer;
import com.pax.pay.constant.Constants;
import com.pax.pay.constant.EUIParamKeys;
import com.pax.pay.emv.EmvListenerImpl;
import com.pax.pay.trans.TransContext;
import com.pax.pay.trans.action.ActionInputPassword;
import com.pax.pay.trans.action.ActionSearchCard.CardInformation;
import com.pax.pay.trans.action.ActionSearchCard.SearchMode;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.CashDolSelect;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.InstalmentData;
import com.pax.pay.trans.model.RedeemConfig;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.EditorActionListener;
import com.pax.pay.utils.TickTimer;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.view.ClssLight;
import com.pax.view.ClssLightsView;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * SearchCardAction中跳转至SearchCardActivity,Intent传递两个参数;
 * <p>
 * {@link com.pax.pay.constant.EUIParamKeys#TRANS_AMOUNT},
 * {@link com.pax.pay.constant.EUIParamKeys #CARD_SEARCH_MODE}
 *
 * <AUTHOR>
 */

public class SearchCardActivity extends BaseActivityWithTickForAction {
    public static final int REQ_ADJUST_TIP = 0;

    private ClssLightsView llClssLight;

    private final AAction currentAction = TransContext.getInstance().getCurrentAction();
    private ActionInputPassword inputPasswordAction = null;

    private TextView tvPrompt; // 输入方式提示

    private EditText edtCardNo; // 输入框
    private EditText expDate; //卡有效期
    private TextView holderName; //显示持卡人姓名

    private ImageButton qrBtn;
    private TextView tvScanQr;
    private ImageView ivPaymentLogo;

    private Button btnConfirm; // 确认按钮

    private ImageView ivSwipe; // 刷卡图标
    private ImageView ivInsert; // 插卡图标
    private ImageView ivTap; // 非接图标

    private LinearLayout llSupportedCard;

    private LinearLayout llSupportedECRRef;
    private LinearLayout llInstalmentHost;
    private TextView tvECRRef;
    private TextView tvInsHost;
    private LinearLayout llPaymentLogo;

    private String navTitle;
    private String amount; // 交易金额
    private String ecrref;
    private String insHost;
    private boolean isEcrMode;
    private boolean isYUUTrans;

    private String cardNo; // 卡号
    private String searchCardPrompt; // 寻卡提示

    private boolean supportManual = false; // 是否支持手输
    private boolean isManualMode = false;
    private boolean isQRMode = false;

    private boolean supportQR = false;

    private EReaderType readerType = null; // 读卡类型

    private Issuer matchedIssuer = null;

    private float iccAdjustPercent = 0;

    // 寻卡成功时，此界面还保留， 在后续界面切换时，还有机会跑到前台，此时按返回键，此activity finish，同时会有两个分支同时进行
    // 如果寻卡成功时， 此标志为true
    private boolean isSuccLeave = false;

    private int retryTime = 3;

    /**
     * 支持的寻卡类型{@link com.pax.pay.trans.action.ActionSearchCard.SearchMode}
     */
    private byte mode; // 寻卡模式

    private PollingResult pollingResult;

    private SearchCardThread searchCardThread = null;

    private boolean isTimeOut = false;

    private TransData transData = null;

    private boolean isFallBack = false;

    private ETransType transType = null;

    private boolean isForceCup = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        displayAmountType();
        FinancialApplication.getApp().register(this);
        runSearchCardThread();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        FinancialApplication.getApp().unregister(this);
        FinancialApplication.getDal().getCardReaderHelper().stopPolling();
    }


    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSearchCardEvent(SearchCardEvent event) {
        switch ((SearchCardEvent.Status) event.getStatus()) {
            case ICC_UPDATE_CARD_INFO:
                tvPrompt.setText(R.string.prompt_default_searchCard_prompt);
                onUpdateCardInfo((EmvListenerImpl.CardInfo) event.getData());
                break;
            case ICC_CONFIRM_CARD_NUM:
                transData = (TransData) event.getData();
                onCardNumConfirm();
                break;
            case PICC_SELECT_CASH_DOL:
                transData = (TransData) event.getData();
                ClickOK();
                break;
            case CLSS_LIGHT_STATUS_NOT_READY:
                llClssLight.setLights(-1, ClssLight.OFF);
                break;
            case CLSS_LIGHT_STATUS_IDLE:
            case CLSS_LIGHT_STATUS_READY_FOR_TXN:
                llClssLight.setLights(0, ClssLight.BLINK);
                break;
            case CLSS_LIGHT_STATUS_PROCESSING:
                llClssLight.setLights(1, ClssLight.ON);
                break;
            case CLSS_LIGHT_STATUS_REMOVE_CARD:
                llClssLight.setLights(2, ClssLight.ON);
                break;
            case CLSS_LIGHT_STATUS_COMPLETE:
                llClssLight.setLights(2, ClssLight.BLINK);
                break;
            case CLSS_LIGHT_STATUS_ERROR:
                llClssLight.setLights(3, ClssLight.BLINK);
                break;
            default:
                break;
        }
    }

    /**
     * load param
     */
    @Override
    protected void loadParam() {
        Bundle bundle = getIntent().getExtras();

        navTitle = getIntent().getStringExtra(EUIParamKeys.NAV_TITLE.toString());
        // 显示金额
        try {
            amount = bundle.getString(EUIParamKeys.TRANS_AMOUNT.toString());
            isEcrMode = bundle.getBoolean(EUIParamKeys.ECR_MODE.toString());
            isYUUTrans = bundle.getBoolean(EUIParamKeys.ECR_YUU.toString());
            isFallBack = bundle.getBoolean(EUIParamKeys.FALLBACK.toString());
            transType = (ETransType) bundle.get(EUIParamKeys.TRANS_TYPE.toString());
            isForceCup = bundle.getBoolean(EUIParamKeys.IS_FORCE_CUP.toString());
            if (amount != null && !amount.isEmpty()) {
                //使用货币code(如RMB)而不是货币symbol(如¥)显示
                amount = CurrencyConverter.convertWithCode(Utils.parseLongSafe(amount, 0));
            }
            if (isEcrMode) {
                ecrref = bundle.getString(EUIParamKeys.ECR_REF.toString());
                if (transType == ETransType.INSTALMENT) {
                    insHost = bundle.getString(EUIParamKeys.ACQUIRER_NAME.toString());
                    Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(insHost);
                    InstalmentData instalmentData = new InstalmentData(acquirer.getInstalPlan(),
                            "INST_" + acquirer.getName().split("_")[2]);
                    insHost = String.format("%s - %02dm", instalmentData.getInstalName(),
                            instalmentData.getInstalMonth());
                    if (!TextUtils.isEmpty(acquirer.getAcqDescription())) {
                        insHost = insHost + "(" + acquirer.getAcqDescription() + ")";
                    }
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "", e);
            amount = null;
        }

        // 寻卡方式
        try {
            mode = bundle.getByte(EUIParamKeys.CARD_SEARCH_MODE.toString(), SearchMode.SWIPE);
            // 是否支持手输卡号
            supportManual = (mode & SearchMode.KEYIN) == SearchMode.KEYIN;
            supportQR = (mode & SearchMode.QR) == SearchMode.QR;
            if (isForceCup) {
                supportQR = false;
            }
            readerType = toReaderType(mode);
        } catch (Exception e) {
            Log.w(TAG, "", e);
        }

        // 获取寻卡提醒
        searchCardPrompt = bundle.getString(EUIParamKeys.SEARCH_CARD_PROMPT.toString());
    }

    /**
     * 获取ReaderType
     *
     * @param mode {@link SearchMode}
     * @return {@link EReaderType}
     */
    private EReaderType toReaderType(byte mode) {
        byte newMode = (byte) (mode & (~SearchMode.KEYIN) & (~SearchMode.QR));
        EReaderType[] types = EReaderType.values();
        for (EReaderType type : types) {
            if (type.getEReaderType() == newMode)
                return type;
        }
        return null;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_bankcard_pay;
    }

    @Override
    protected String getTitleString() {
        return navTitle;
    }

    @Override
    protected void initViews() {
        initDefaultViews();
    }

    /**
     * 默认寻卡界面初始化
     */
    private void initDefaultViews() {
        llClssLight = (ClssLightsView) findViewById(R.id.clssLight);
        llClssLight.setVisibility(View.GONE);
        if ((mode & SearchMode.WAVE) == SearchMode.WAVE) {
            llClssLight.setVisibility(View.VISIBLE);
            llClssLight.setLights(0, ClssLight.BLINK);
        }

        LinearLayout llAmount = (LinearLayout) findViewById(R.id.amount_layout);

        if (isYUUTrans) {
            llAmount.setVisibility(View.GONE);
        } else {
            if (amount == null || amount.isEmpty()) { // 余额查询不显示金额
                llAmount.setVisibility(View.INVISIBLE);
            } else {
                TextView tvAmount = (TextView) findViewById(R.id.amount_txt); // 只显示交易金额
                tvAmount.setText(amount);
            }
        }

        llSupportedECRRef = (LinearLayout) findViewById(R.id.ecr_ref_layout);
        tvECRRef = (TextView) findViewById(R.id.ecr_ref_txt);
        llInstalmentHost = (LinearLayout) findViewById(R.id.ecr_ins_host_layout);
        tvInsHost = (TextView) findViewById(R.id.ecr_ins_host_text);

        if (isEcrMode) {
            llSupportedECRRef.setVisibility(View.VISIBLE);
            tvECRRef.setText(ecrref);
            if (transType == ETransType.INSTALMENT) {
                llInstalmentHost.setVisibility(View.VISIBLE);
                tvInsHost.setText(insHost);
            }
        } else {
            llSupportedECRRef.setVisibility(View.GONE);
            llInstalmentHost.setVisibility(View.GONE);
        }

        edtCardNo = (EditText) findViewById(R.id.bank_card_number);// 初始为卡号输入框
        expDate = (EditText) findViewById(R.id.bank_card_expdate);//卡有效期输入框

        holderName = (TextView) findViewById(R.id.bank_card_holder_name);
        holderName.setVisibility(View.GONE);

        btnConfirm = (Button) findViewById(R.id.ok_btn);
        btnConfirm.setEnabled(false);

        tvPrompt = (TextView) findViewById(R.id.tv_prompt_readcard);

        ivSwipe = (ImageView) findViewById(R.id.iv_swipe);
        ivInsert = (ImageView) findViewById(R.id.iv_insert);
        ivTap = (ImageView) findViewById(R.id.iv_tap);

        tvPrompt.setText(searchCardPrompt);
        if (supportManual) {
            edtCardNo.setHint(R.string.prompt_card_num_manual);
            edtCardNo.setHintTextColor(getResources().getColor(R.color.secondary_text_light));
            edtCardNo.setClickable(true);// 支持手输卡号
            edtCardNo.setImeOptions(EditorInfo.IME_FLAG_NO_EXTRACT_UI);
            edtCardNo.addTextChangedListener(new CardNoWatcher());
            edtCardNo.setFilters(new InputFilter[]{new InputFilter.LengthFilter(19 + 4)});// 4为卡号分隔符个数
            edtCardNo.setCursorVisible(true);

            expDate.setVisibility(View.GONE);
            expDate.setClickable(true);// 支持手输卡号
            expDate.setImeOptions(EditorInfo.IME_FLAG_NO_EXTRACT_UI);
            expDate.addTextChangedListener(new ExpDateWatcher());
            expDate.setFilters(new InputFilter[]{new InputFilter.LengthFilter(4 + 1)});// 4为卡号分隔符个数
            expDate.setCursorVisible(true);

            edtCardNo.setOnEditorActionListener(new EditorActionListener() {
                @Override
                protected void onKeyOk() {
                    processManualCardNo();
                    //ecr模式下显示ECR Reference#会导致手输卡号时卡号输入框与扫码框重叠
                    if (isEcrMode) {
                        qrBtn.setVisibility(View.GONE);
                        tvScanQr.setVisibility(View.GONE);
                    }
                    llPaymentLogo.setVisibility(View.GONE);
                }

                @Override
                protected void onKeyCancel() {
                    //do nothing
                    if (isEcrMode) {
                        qrBtn.setVisibility(View.VISIBLE);
                        tvScanQr.setVisibility(View.VISIBLE);
                    }
                    llPaymentLogo.setVisibility(View.VISIBLE);
                }
            });
            expDate.setOnEditorActionListener(new EditorActionListener() {
                @Override
                protected void onKeyOk() {
                    processManualExpDate();
                }

                @Override
                protected void onKeyCancel() {
                    //do nothing
                }
            });
        } else {
            edtCardNo.setEnabled(false);// 不支持手输入卡号
            expDate.setEnabled(false);// 不支持手输入卡号
        }

        qrBtn = (ImageButton) findViewById(R.id.qr_scanner);
        tvScanQr = (TextView) findViewById(R.id.tv_scan_qr);
        if (supportQR && FinancialApplication.getSysParam()
                .get(SysParam.BooleanParam.EDC_ENABLE_QR_PAYMENT)) {
            qrBtn.setOnClickListener(this);
            qrBtn.setFocusable(false);
        } else {
            qrBtn.setVisibility(View.GONE);
            tvScanQr.setVisibility(View.GONE);
        }

        btnConfirm.setVisibility(View.INVISIBLE);
        llSupportedCard = (LinearLayout) findViewById(R.id.supported_card_prompt);
        llPaymentLogo = (LinearLayout) findViewById(R.id.ll_payment_logo_prompt);
        ivPaymentLogo = (ImageView) findViewById(R.id.iv_payment_logo);
        Bitmap image = null;
        InputStream is;
        if (Utils.isFileExist(Component.PAYMENT_LOGO)) {
            try {
                is = FinancialApplication.getApp().openFileInput(Component.PAYMENT_LOGO);
                image = BitmapFactory.decodeStream(is);
                is.close();
            } catch (IOException e) {
                Log.e(TAG, "", e);
            }
            ivPaymentLogo.setImageBitmap(image);
        }
        //Payment Logo 只需要在 SALE 交易流程顯示
        if (isEcrMode || !FinancialApplication.getSysParam()
                .get(SysParam.BooleanParam.EDC_ENABLE_PAYMENT_LOGO)
                || transType != ETransType.SALE || isForceCup) {
            llPaymentLogo.setVisibility(View.GONE);
        }

        setSearchCardImage((mode & SearchMode.SWIPE) == SearchMode.SWIPE,
                (mode & SearchMode.INSERT) == SearchMode.INSERT,
                (mode & SearchMode.WAVE) == SearchMode.WAVE);
    }

    /**
     * set listeners
     */
    @Override
    protected void setListeners() {
        btnConfirm.setOnClickListener(this);
    }

    /**
     * 点击事件
     *
     * @param v View
     */
    @Override
    public void onClickProtected(View v) {
        switch (v.getId()) { // manual input case: get click event from IME_ACTION_DONE, the button is always hidden.
            case R.id.ok_btn:
                onOkClicked();
                break;
            case R.id.qr_scanner:
                isQRMode = true;
                llClssLight.setLights(-1, ClssLight.OFF);
                FinancialApplication.getDal().getCardReaderHelper().stopPolling();
                finish(new ActionResult(TransResult.SUCC, new CardInformation(SearchMode.QR)));
                break;
        }
    }

    /**
     * on options item selected
     *
     * @param item MenuItem
     * @return boolean
     */
    @Override
    protected boolean onOptionsItemSelectedSub(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onHeaderBackClicked();
            return true;
        }
        return super.onOptionsItemSelectedSub(item);
    }

    /**
     * on activity result
     *
     * @param requestCode int
     * @param resultCode  int
     * @param data        Intent
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQ_ADJUST_TIP && data != null) { //AET-82
            updateAmount(data);
        } else {
            cancel(TransResult.ERR_USER_CANCEL);
        }
    }

    /**
     * update amount
     *
     * @param data Intent
     */
    private void updateAmount(@NonNull Intent data) {
        amount = data.getStringExtra(EUIParamKeys.TRANS_AMOUNT.toString());
        String tipAmount = data.getStringExtra(EUIParamKeys.TIP_AMOUNT.toString());
        TextView tvAmount = (TextView) findViewById(R.id.amount_txt);
        tvAmount.setText(amount);
        FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.CARD_NUM_CONFIRM_SUCCESS,
                new String[]{amount, tipAmount}));
        isSuccLeave = true; //AET-106
    }

    /**
     * run search card thread
     */
    private void runSearchCardThread() {
        if (searchCardThread != null && searchCardThread.getState() == Thread.State.TERMINATED) {
            FinancialApplication.getDal().getCardReaderHelper().stopPolling();
            searchCardThread.interrupt();
        }
        isManualMode = false;
        isQRMode = false;
        searchCardThread = new SearchCardThread();
        searchCardThread.start();
    }

    /**
     * 寻卡线程
     */
    private class SearchCardThread extends Thread {

        @Override
        public void run() {
            try {
                ICardReaderHelper cardReaderHelper = FinancialApplication.getDal().getCardReaderHelper();
                if (readerType == null) {
                    return;
                }
                pollingResult = cardReaderHelper.polling(readerType, 60 * 1000);
                cardReaderHelper.stopPolling();
                if (pollingResult.getOperationType() == PollingResult.EOperationType.CANCEL) {
                    FinancialApplication.getApp().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            onReadCardCancel(TransResult.ERR_USER_CANCEL);
                        }
                    });
                } else if (pollingResult.getOperationType() == PollingResult.EOperationType.TIMEOUT) {
                    FinancialApplication.getApp().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            onReadCardCancel(TransResult.ERR_TIMEOUT);
                        }
                    });
                } else if (pollingResult.getOperationType() == PollingResult.EOperationType.OK) {
                    FinancialApplication.getApp().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            onReadCardOk();
                        }
                    });
                }

            } catch (MagDevException | IccDevException | PiccDevException e) {
                Log.e(TAG, "", e);
                // 读卡失败处理
                FinancialApplication.getApp().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        onReadCardError();
                    }
                });
            }

        }

        /**
         * 完成读卡
         */
        private void onReadCardOk() {
            //case of allowing Fallback
            if (pollingResult.getReaderType() == EReaderType.MAG) {
                tvPrompt.setText(getString(R.string.prompt_default_searchCard_prompt));
                if ((mode & SearchMode.INSERT) == SearchMode.INSERT && TrackUtils.isIcCard(pollingResult.getTrack2())) {
                    Device.beepErr();
                    ToastUtils.showMessage(R.string.prompt_ic_card_input);
                    mode &= ~SearchMode.SWIPE;
                    readerType = toReaderType(mode);
                    setSearchCardImage((mode & SearchMode.SWIPE) == SearchMode.SWIPE,
                            (mode & SearchMode.INSERT) == SearchMode.INSERT,
                            (mode & SearchMode.WAVE) == SearchMode.WAVE);
                    runSearchCardThread();
                    return;
                }

                Device.beepPrompt();
                // 有时刷卡成功，单没有磁道II，做一下防护
                String track2 = pollingResult.getTrack2();
                String track1 = pollingResult.getTrack1();
                String pan = TrackUtils.getPan(track2);
                String exp = TrackUtils.getExpDate(track2);
                String cardholder = TrackUtils.getHolderName(track1);

                //发卡行归属校验
                matchedIssuer = FinancialApplication.getAcqManager().findIssuerByPan(pan);
                if (matchedIssuer == null) {
                    finish(new ActionResult(TransResult.ERR_CARD_UNSUPPORTED, null));
                    return;
                }

                //由EDC 开关控制是否支持V/M Fallback
                //V/M Fallback开关不影响银联销售
                if (isFallBack && FinancialApplication.getSysParam()
                        .get(SysParam.BooleanParam.EDC_ENABLE_VM_FALLBACK_BLOCK) && !isForceCup) {
                    if (matchedIssuer.getName().equals("VISA")
                            || matchedIssuer.getName().equals("MASTER")
                            || matchedIssuer.getName().equals("HASE_VISA")
                            || matchedIssuer.getName().equals("HASE_MC")) {
                        finish(new ActionResult(TransResult.ERROR_CARD_READ, null));
                        return;
                    }
                }
                //卡号校验
                if (!Issuer.validPan(matchedIssuer, pan)) {
                    finish(new ActionResult(TransResult.ERR_CARD_INVALID, null));
                    return;
                }

                //日期显示格式为（MMYY）
                if (exp == null || exp.length() != 4) {
                    Device.beepErr();
                    setSearchCardImage((mode & SearchMode.SWIPE) == SearchMode.SWIPE,
                            (mode & SearchMode.INSERT) == SearchMode.INSERT,
                            (mode & SearchMode.WAVE) == SearchMode.WAVE);
                    tvPrompt.setText(getString(R.string.prompt_default_searchCard_prompt));
                    runSearchCardThread();
                    return;
                }
                //卡过期时间校验
                if (!Issuer.validCardExpiry(matchedIssuer, exp)) {
                    finish(new ActionResult(TransResult.ERR_CARD_EXPIRED, null));
                    return;
                }

                edtCardNo.setEnabled(false);
                edtCardNo.setText(PanUtils.separateWithSpace(pan));
                expDate.setVisibility(View.VISIBLE);
                expDate.setEnabled(false);

                String content = edtCardNo.getText().toString().replace(" ", "");
                if (content == null || content.isEmpty()) {
                    FinancialApplication.getApp().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            onReadCardError();
                        }
                    });
                    return;
                }

                //MM/YY
                exp = exp.substring(2, 4) + "/" + exp.substring(0, 2);
                expDate.setText(exp);

                //持卡人姓名为非空时才可见
                if (cardholder != null) {
                    holderName.setVisibility(View.VISIBLE);
                    String name = cardholder.replace("/", "");
                    holderName.setText(name.trim());
                }

                llSupportedCard.setVisibility(View.INVISIBLE);
                llPaymentLogo.setVisibility(View.INVISIBLE);
                qrBtn.setEnabled(false);
                qrBtn.setVisibility(View.INVISIBLE);
                tvScanQr.setVisibility(View.GONE);
                llClssLight.setLights(-1, ClssLight.OFF);
                //AET-136
                tickTimer.start();
                Utils.wakeupScreen(TickTimer.DEFAULT_TIMEOUT);
                confirmBtnChange();
            } else if (pollingResult.getReaderType() == EReaderType.ICC) {
                //需要通过EMV才能获取到卡号等信息,所以先在EMV里面获取到信息，再到case CARD_NUM_CONFIRM中显示
                disableNonCardView();
                llClssLight.setLights(-1, ClssLight.OFF);
                finish(new ActionResult(TransResult.SUCC, new CardInformation(SearchMode.INSERT)));
            } else if (pollingResult.getReaderType() == EReaderType.PICC) {
                disableNonCardView();
                llClssLight.setVisibility(View.VISIBLE);
                finish(new ActionResult(TransResult.SUCC, new CardInformation(SearchMode.WAVE)));
            }
        }

        /**
         * disable card view
         */
        private void disableNonCardView() {
            llSupportedCard.setVisibility(View.INVISIBLE);
            llPaymentLogo.setVisibility(View.INVISIBLE);
            edtCardNo.setEnabled(false);
            expDate.setEnabled(false);
            qrBtn.setEnabled(false);
            qrBtn.setVisibility(View.INVISIBLE);
            tvScanQr.setVisibility(View.GONE);
        }

    }

    /**
     * 卡号分割及输入长度检查
     */
    private class CardNoWatcher implements TextWatcher {

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            tickTimer.start();
            if (s.length() == 0) {
                return;
            }

            if (before < count) {
                String card = s.toString().replace(" ", "");
                card = card.replaceAll("(\\d{4}(?!$))", "$1 ");
                if (!card.equals(s.toString())) {
                    edtCardNo.setText(card);
                    boolean isMiddle = (start + count) < (s.length());
                    //保持光标位置，在末尾则保持在末尾，在中间则保持在中间，避免在中间位置编辑卡号时每输入一个数字光标就跳转至卡号末尾
                    if (!isMiddle) {
                        edtCardNo.setSelection(card.length());
                    } else {
                        edtCardNo.setSelection(start + 1);
                    }
                }
            }
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            //do nothing
        }

        @Override
        public void afterTextChanged(Editable s) {
            //do nothing
        }
    }

    /**
     * ExpDateWatcher
     */
    private class ExpDateWatcher implements TextWatcher {

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            tickTimer.start();
            if (s.length() == 0)
                return;
            String exp = s.toString().replace("/", "");
            exp = exp.replaceAll("(\\d{2}(?!$))", "$1/");
            if (!exp.equals(s.toString())) {
                expDate.setText(exp);
                expDate.setSelection(exp.length());
            }
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            //do nothing
        }

        @Override
        public void afterTextChanged(Editable s) {
            //do nothing
        }
    }

    /**
     * on read card cancel
     * operationType对应ActionResult中的返回码
     */
    private void onReadCardCancel(int operationType) {
        if (!isManualMode && !isQRMode) { // AET-179
            Log.i(TAG, "SEARCH CARD CANCEL");
            FinancialApplication.getDal().getCardReaderHelper().stopPolling();
            finish(new ActionResult(operationType, null));
        }
    }

    /**
     * on read card error
     */
    private void onReadCardError() {
        ToastUtils.showMessage(R.string.prompt_please_retry);
        runSearchCardThread();
    }

    /**
     * on edit card no
     */
    private void onEditCardNo() {
        isManualMode = true;
        FinancialApplication.getDal().getCardReaderHelper().stopPolling();

        llClssLight.setLights(-1, ClssLight.OFF);
        cardNo = edtCardNo.getText().toString().replace(" ", "");
        expDate.setVisibility(View.VISIBLE);
        expDate.setText("");
        expDate.requestFocus();
        //KeyboardUtils.showSystemKeyboard(this, expDate); //removed AET-228
    }

    /**
     * on edit card no error
     */
    private void onEditCardNoError() {
        ToastUtils.showMessage(R.string.prompt_card_num_err);
        edtCardNo.setText("");
        edtCardNo.requestFocus();
    }

    /**
     * on edit date
     */
    private void onEditDate() {
        if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_MANUAL_PWD)) {
            runInputMerchantPwdAction();
        } else {
            FinancialApplication.getApp().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onVerifyManualPan();
                }
            });
        }
    }

    /**
     * on edit data error
     */
    private void onEditDateError() {
        ToastUtils.showMessage(R.string.prompt_card_date_err);
        expDate.setText("");
        expDate.requestFocus();
    }

    /**
     * on verify manual pan
     */
    private void onVerifyManualPan() {
        String date = expDate.getText().toString().replace("/", "");
        //修复bug-5413：避免在未点击确认键的情况下编辑卡号无法获取最新卡号的问题
        cardNo = edtCardNo.getText().toString().replace(" ", "");
        if (!date.isEmpty()) {
            date = date.substring(2) + date.substring(0, 2);// 将MMyy转换成yyMM
        }

        matchedIssuer = FinancialApplication.getAcqManager().findIssuerByPan(cardNo);
        if (matchedIssuer == null) {
            finish(new ActionResult(TransResult.ERR_CARD_UNSUPPORTED, null));
            return;
        }

        if (!matchedIssuer.isAllowManualPan()) {
            finish(new ActionResult(TransResult.ERR_UNSUPPORTED_FUNC, null));
            return;
        }

        if (!Issuer.validPan(matchedIssuer, cardNo)) {
            finish(new ActionResult(TransResult.ERR_CARD_INVALID, null));
            return;
        }

        if (!Issuer.validCardExpiry(matchedIssuer, date)) {
            finish(new ActionResult(TransResult.ERR_CARD_EXPIRED, null));
            return;
        }

        CardInformation cardInfo = new CardInformation(SearchMode.KEYIN, cardNo, date, matchedIssuer);
        finish(new ActionResult(TransResult.SUCC, cardInfo));
    }

    /**
     * on update card info
     *
     * @param cardInfo EmvListenerImpl.CardInfo
     */
    private void onUpdateCardInfo(EmvListenerImpl.CardInfo cardInfo) {
        edtCardNo.setText(PanUtils.separateWithSpace(cardInfo.getCardNum()));
        if (cardInfo.getHolderName() != null) {
            holderName.setVisibility(View.VISIBLE);
            String name = cardInfo.getHolderName().replace("/", "");
            holderName.setText(name.trim());
        }
        if (cardInfo.getExpDate() != null) {
            String exp = cardInfo.getExpDate().substring(2, 4) + "/" + cardInfo.getExpDate().substring(0, 2);
            expDate.setText(exp);
            expDate.setVisibility(View.VISIBLE);
        }
        iccAdjustPercent = cardInfo.getAdjustPercent();
    }

    /**
     * on card num confirm
     */
    private void onCardNumConfirm() {
        isSuccLeave = false;
        edtCardNo.setClickable(false);
        //AET-120
        tickTimer.start();
        //AET-135
        Utils.wakeupScreen(TickTimer.DEFAULT_TIMEOUT);
        confirmBtnChange();
    }

    /**
     * 设置图标显示
     *
     * @param mag  enable mag
     * @param icc  enable icc
     * @param picc enable picc
     */
    private void setSearchCardImage(boolean mag, boolean icc, boolean picc) {
        ivSwipe.setImageResource(mag ? R.drawable.swipe_card : R.drawable.no_swipe_card);
        ivInsert.setImageResource(icc ? R.drawable.insert_card : R.drawable.no_insert_card);
        ivTap.setImageResource(picc ? R.drawable.tap_card : R.drawable.no_tap_card);
    }

    /**
     * on key back down
     *
     * @return boolean
     */
    @Override
    protected boolean onKeyBackDown() {
        edtCardNo.clearFocus(); // avoid android keyboard shown out
        expDate.clearFocus(); // avoid android keyboard shown out
        cancel(TransResult.ERR_USER_CANCEL);
        return true;
    }

    /**
     * on key down
     *
     * @param keyCode 按键id
     * @param event   事件
     * @return boolean
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return isSuccLeave || super.onKeyDown(keyCode, event);
    }

    @Override
    public void finish(ActionResult result) {
        FinancialApplication.getDal().getCardReaderHelper().stopPolling();

        if (result.getRet() == TransResult.SUCC) {
            isSuccLeave = true;
        }
        super.finish(result);
    }

    /**
     * on header back clicked
     */
    private void onHeaderBackClicked() {
        if (isSuccLeave) {
            return;
        }
        if (pollingResult != null && pollingResult.getReaderType() == EReaderType.ICC) {
            FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.CARD_NUM_CONFIRM_ERROR));
        } else {
            FinancialApplication.getApp().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onReadCardCancel(TransResult.ERR_USER_CANCEL);
                }
            });
        }
    }

    /**
     * on ok clicked
     */
    private void onOkClicked() {
        tickTimer.stop();
        btnConfirm.setEnabled(false);
        ClickOK();
    }

    /**
     * click ok
     */
    private void ClickOK() {
        if (transData != null) {
            Acquirer acquirer = transData.getAcquirer();
            if (!Utils.checkAcquirerSettleStatus(acquirer.getName())) {
                if (transData.getEnterMode() == TransData.EnterMode.INSERT) {
                    FinancialApplication.getApp().doEvent(new SettlePendingEvent(SettlePendingEvent.Status
                            .SETTLE_PENDING_FOR_INSERT));
                } else if (transData.getEnterMode() == TransData.EnterMode.CLSS) {
                    FinancialApplication.getApp().doEvent(new SettlePendingEvent(SettlePendingEvent.Status
                            .SETTLE_PENDING_FOR_CLSS));
                }
                return;
            }

            ETransType transType = transData.getTransType();

            if (!Component.chkIssuerOption(transType, transData.getIssuer())) {
                DialogUtils.showErrMessage(SearchCardActivity.this, transType.getTransName(),
                        getString(R.string.err_unsupported_trans), new DialogInterface.OnDismissListener() {
                            @Override
                            public void onDismiss(DialogInterface dialog) {
                                FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.CARD_NUM_CONFIRM_ERROR));
                            }
                        }, Constants.FAILED_DIALOG_SHOW_TIME);
                return;
            }

            if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                //如果强制走银联后台, 则不应支持Redeem
//                if (transData.isForceCup()) {
//                    afterOkClicked();
//                    return;
//                }

                //如果ECR Redeem开关关闭则默认取消Redeem
                if (transData.isEcrMode() && transData.getEcrLoyaltyCode().equals("00") && !FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ECR_ENABLE_REDEEM)) {
                    transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                    transData.setRedemptionFlag(RedeemConfig.getRedemptionFlag(RedeemConfig.REDEEM_NONE));
                    afterOkClicked();
                    return;
                }

                CashDolSelect cashDolSelect = new CashDolSelect(this, transData);

                //如果ECR LoyaltyCode == "03",直接确认选择Redeem
                if (transData.isEcrMode() && transData.getEcrLoyaltyCode().equals("03")) {
                    cashDolSelect.selectRedeem();
                    afterOkClicked();
                    return;
                }

                cashDolSelect.setRedeemListener(new CashDolSelect.RedeemListener() {
                    @Override
                    public void onRedeemSelected(boolean isUserCanceled) {
                        if (isUserCanceled) {
                            if (transData.getEnterMode() == TransData.EnterMode.CLSS) {
                                FinancialApplication.getApp().doEvent(new ClssCallbackEvent(ClssCallbackEvent.Status.CARD_NUM_CONFIRM_ERROR));
                            } else {
                                FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.CARD_NUM_CONFIRM_ERROR));
                            }
                        } else {
                            afterOkClicked();
                        }
                    }
                });

                switch (transType) {
                    case SALE:
                        transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);  //change the transtype, used the Redeem pack.
                        showRedeemDialog(cashDolSelect);
                        break;
                    case PURE_REDEEM:
                        if (!RedeemConfig.isCahsDolChoice()) {
                            transData.setSpcRedemptionFuncFlag(RedeemConfig.SF_REDM_ONLY_WITHOUT_ADVANCE);
                            afterOkClicked();
                        } else {
                            showAdvancedRedeemDialog();
                        }
                        break;
                    case REDEEM_INST:
                        cashDolSelect.selectRedeem();
                        break;
                    default:
                        afterOkClicked();
                        break;
                }
                return;
            } else {
                if (transType == ETransType.PURE_REDEEM
                        || transType == ETransType.REDEEM_INST
                        || transType == ETransType.MULTIPLE_UP
                        || transType == ETransType.INST_MULTI_UP
                        || transType == ETransType.ONLINE_ENQUIRY) {
                    final Event event = transData.getEnterMode() == TransData.EnterMode.INSERT ?
                            new EmvCallbackEvent(EmvCallbackEvent.Status.CARD_NUM_CONFIRM_ERROR) :
                            new ClssCallbackEvent(ClssCallbackEvent.Status.CARD_NUM_CONFIRM_ERROR);
                    DialogUtils.showErrMessage(SearchCardActivity.this, transType.getTransName(),
                            getString(R.string.err_card_unsupported), new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    FinancialApplication.getApp().doEvent(event);
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                    return;
                }
            }
        }

        afterOkClicked();
    }

    /**
     * show redeem dialog
     *
     * @param cashDolSelect CashDolSelect
     */
    private void showRedeemDialog(final CashDolSelect cashDolSelect) {
        CustomAlertDialog dialog = new CustomAlertDialog(this, CustomAlertDialog.NORMAL_TYPE);
        dialog.setCancelClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                transData.setRedemptionFlag(RedeemConfig.getRedemptionFlag(RedeemConfig.REDEEM_NONE));
                afterOkClicked();
            }
        });
        dialog.setConfirmClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                cashDolSelect.selectRedeem();
            }
        });
        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                cancel(TransResult.ERR_USER_CANCEL);
            }
        });
        dialog.show();
        dialog.setNormalText(getString(R.string.trans_is_redeem));
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
        dialog.setCanceledOnTouchOutside(false);
    }

    /**
     * show advance redeem dialog
     */
    private void showAdvancedRedeemDialog() {
        CustomAlertDialog dialog = new CustomAlertDialog(this, CustomAlertDialog.NORMAL_TYPE);
        dialog.setCancelClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                transData.setSpcRedemptionFuncFlag(RedeemConfig.SF_REDM_ONLY_WITHOUT_ADVANCE);
                afterOkClicked();
            }
        });
        dialog.setConfirmClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
                transData.setSpcRedemptionFuncFlag(RedeemConfig.SF_REDM_ONLY_WITH_ADVANCE);
                afterOkClicked();
            }
        });
        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                cancel(TransResult.ERR_USER_CANCEL);
            }
        });
        dialog.show();
        dialog.setNormalText(getString(R.string.prompt_is_advance_redeem));
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
        dialog.setCanceledOnTouchOutside(false);
    }

    /**
     * cancel
     * operationType 代表对应取消对应的动作（超时或者手动取消）
     */
    private void cancel(int operationType) {
        FinancialApplication.getDal().getCardReaderHelper().stopPolling();
        if (transData != null && transData.getEnterMode() == TransData.EnterMode.INSERT) {
            FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent
                    .Status.CARD_NUM_CONFIRM_ERROR));
        } else if (transData != null && transData.getEnterMode() == TransData.EnterMode.CLSS) {
            FinancialApplication.getApp().doEvent(new ClssCallbackEvent(ClssCallbackEvent
                    .Status.CARD_NUM_CONFIRM_ERROR));
        } else {
            //FinancialApplication.getApp()
            //        .doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.TIMEOUT));
            onReadCardCancel(operationType);
        }
    }

    /**
     * after ok clicked
     */
    private void afterOkClicked() {
        if (pollingResult != null && pollingResult.getReaderType() == EReaderType.ICC) {
            boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP)
                    && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_TIP_TYPE);
            if (transData.getAcquirer().getName().equals("HASE_CUP")) { //Issue 94 Disable the CUP tips as HASE host not yet support
                enableTip = false;
            }
            if (enableTip && iccAdjustPercent > 0) {
                long baseAmountLong = CurrencyConverter.parse(amount);
                Intent intent = new Intent(SearchCardActivity.this, AdjustTipActivity.class);
                intent.putExtra(EUIParamKeys.NAV_TITLE.toString(), navTitle);
                intent.putExtra(EUIParamKeys.TRANS_AMOUNT.toString(), String.valueOf(baseAmountLong));
                intent.putExtra(EUIParamKeys.TIP_PERCENT.toString(), iccAdjustPercent);
                intent.putExtra(EUIParamKeys.CARD_MODE.toString(), EReaderType.ICC.toString());
                //如果是三方应用传入，则还会携带TipAmount，需要传入下个页面显示
                if (transData.isFromThirdParty())
                    intent.putExtra(EUIParamKeys.TIP_AMOUNT.toString(), transData.getTipAmount());
                transData.setIsPreSaleTips(true);
                startActivityForResult(intent, REQ_ADJUST_TIP);
            } else {
                FinancialApplication.getApp().doEvent(new EmvCallbackEvent(EmvCallbackEvent.Status.CARD_NUM_CONFIRM_SUCCESS));
            }
        } else if (pollingResult != null && pollingResult.getReaderType() == EReaderType.MAG) {
            // 填写信息校验
            CardInformation cardInfo = new CardInformation(SearchMode.SWIPE, pollingResult.getTrack1(), pollingResult.getTrack2(),
                    pollingResult.getTrack3(), TrackUtils.getPan(pollingResult.getTrack2()), matchedIssuer);
            Issuer issuer = FinancialApplication.getAcqManager().findIssuerByPan(cardInfo.getPan());
            if (transData != null && transData.isEcrMode() && transData.getCardPromotion() != null) {
                String cardPromotion = transData.getCardPromotion();
                if ("07".equals(cardPromotion)) {
                    List<String> promotionCardBins = Utils.splitPromotionCardBins(transData.getOtherPromotionCardBin());
                    if (!Utils.isCardBinMatched(cardInfo.getPan(), promotionCardBins)) {
                        finish(new ActionResult(TransResult.ERR_CARD_TYPE_MISMATCH, null));
                        return;
                    }
                } else {
                    if (!Utils.isCardPromotion(issuer, cardPromotion)) {
                        finish(new ActionResult(TransResult.ERR_CARD_TYPE_MISMATCH, null));
                        return;
                    }
                }
            }
            finish(new ActionResult(TransResult.SUCC, cardInfo));
        } else if (pollingResult != null && pollingResult.getReaderType() == EReaderType.PICC) {
            FinancialApplication.getApp().doEvent(new ClssCallbackEvent(ClssCallbackEvent.Status
                    .CARD_NUM_CONFIRM_SUCCESS));
        }
    }

    /**
     * process manual card no
     */
    private void processManualCardNo() {
        final String content = edtCardNo.getText().toString().replace(" ", "");
        if (content.length() < 13) {
            onEditCardNoError();
        } else {
            onEditCardNo();
        }
    }

    /**
     * process manual expdate
     */
    private void processManualExpDate() {
        final String content = expDate.getText().toString().replace(" ", "");
        if (content.isEmpty()) {
            onEditDate();
        } else {
            if (dateProcess(content)) {
                onEditDate();
            } else {
                onEditDateError();
            }
        }
    }

    /**
     * data process
     *
     * @param content String
     * @return boolean
     */
    private boolean dateProcess(String content) {
        final String mmYY = "MM/yy";
        if (content.length() != mmYY.length()) {
            return false;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat(mmYY, Locale.US);
        dateFormat.setLenient(false);
        try {
            dateFormat.parse(content);
        } catch (ParseException e) {
            Log.w(TAG, "", e);
            return false;
        }

        return true;
    }

    /**
     * confirm button change
     */
    private void confirmBtnChange() {
        String content = edtCardNo.getText().toString();
        if (!content.isEmpty()) {
            btnConfirm.setEnabled(true);
            btnConfirm.setVisibility(View.VISIBLE);
        } else {
            btnConfirm.setEnabled(false);
            btnConfirm.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * run input merchant password action
     */
    private void runInputMerchantPwdAction() {
        inputPasswordAction = new ActionInputPassword(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionInputPassword) action).setParam(SearchCardActivity.this, 6,
                        getString(R.string.prompt_manual_pwd), null, false);
                ((ActionInputPassword) action).setParam(TransResult.ERR_USER_CANCEL);
            }
        });

        inputPasswordAction.setEndListener(new AAction.ActionEndListener() {

            @Override
            public void onEnd(AAction action, ActionResult result) {
                TransContext.getInstance().setCurrentAction(currentAction);

                if (result.getRet() != TransResult.SUCC) {
                    //AET-156
                    finish(new ActionResult(result.getRet(), null));
                    return;
                }

                String data = EncUtils.pwdSha((String) result.getData());
                if (!data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_MANUAL_PWD)) && !data.equals(FinancialApplication.getSysParam().get(SysParam.StringParam.SEC_SYS_PWD))) {
                    //retry three times
                    retryTime--;
                    if (retryTime > 0) {
                        // AET-110, AET-157
                        DialogUtils.showErrMessage(SearchCardActivity.this, getString(R.string.trans_password),
                                getString(R.string.err_password), new DialogInterface.OnDismissListener() {
                                    @Override
                                    public void onDismiss(DialogInterface dialog) {
                                        FinancialApplication.getApp().runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                //AET-158
                                                if (!isTimeOut) {
                                                    onEditDate();
                                                }
                                            }
                                        });
                                    }
                                }, Constants.FAILED_DIALOG_SHOW_TIME);
                    } else {
                        finish(new ActionResult(TransResult.ERR_PASSWORD, null));
                    }
                    return;
                }

                FinancialApplication.getApp().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        onVerifyManualPan();
                    }
                });
            }
        });

        inputPasswordAction.execute();
    }

    /**
     * on timer finish
     */
    @Override
    protected void onTimerFinish() {
        TransContext.getInstance().setCurrentAction(currentAction);
        currentAction.setFinished(false); //AET-253
        if (inputPasswordAction != null)
            inputPasswordAction.setResult(new ActionResult(TransResult.ERR_TIMEOUT, null));
        isTimeOut = true;
        cancel(TransResult.ERR_TIMEOUT);
        super.onTimerFinish();
    }

    /**
     * display amount type
     */
    private void displayAmountType() {
        if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP)
                || !FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_TIP_TYPE)) {
            TextView amountName = (TextView) findViewById(R.id.amount_name);
            amountName.setText(getString(R.string.amount));
        }
    }
}
