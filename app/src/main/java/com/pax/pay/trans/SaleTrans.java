/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay.trans;

import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.util.Log;

import com.pax.abl.core.AAction;
import com.pax.abl.core.AAction.ActionStartListener;
import com.pax.abl.core.ActionResult;
import com.pax.commonlib.LogUtils;
import com.pax.ecrsdk.EcrConstants;
import com.pax.ecrsdk.message.request.Request;
import com.pax.edc.R;
import com.pax.edc.opensdk.BaseRequest;
import com.pax.edc.opensdk.SaleRequest;
import com.pax.edc.opensdk.TransResult;
import com.pax.eemv.entity.CTransResult;
import com.pax.eemv.enums.ECvmResult;
import com.pax.eemv.enums.ETransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.constant.Constants;
import com.pax.pay.emv.EmvQr;
import com.pax.pay.emv.EmvTags;
import com.pax.pay.emv.EmvTransProcess;
import com.pax.pay.emv.clss.ClssTransProcess;
import com.pax.pay.trans.action.ActionAdjustTip;
import com.pax.pay.trans.action.ActionCashDolSelect;
import com.pax.pay.trans.action.ActionClssPreProc;
import com.pax.pay.trans.action.ActionClssProcess;
import com.pax.pay.trans.action.ActionEmvProcess;
import com.pax.pay.trans.action.ActionEnterAmount;
import com.pax.pay.trans.action.ActionEnterAuthCode;
import com.pax.pay.trans.action.ActionEnterPin;
import com.pax.pay.trans.action.ActionInputRefNo;
import com.pax.pay.trans.action.ActionOfflineSend;
import com.pax.pay.trans.action.ActionScanCode;
import com.pax.pay.trans.action.ActionSearchCard;
import com.pax.pay.trans.action.ActionSearchCard.CardInformation;
import com.pax.pay.trans.action.ActionSearchCard.SearchMode;
import com.pax.pay.trans.action.ActionSelectCurrency;
import com.pax.pay.trans.action.ActionSignature;
import com.pax.pay.trans.action.ActionTransOnline;
import com.pax.pay.trans.action.activity.SelectCurrencyActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.ETransType;
import com.pax.pay.trans.model.RedeemConfig;
import com.pax.pay.trans.model.TransData;
import com.pax.pay.trans.model.TransData.EnterMode;
import com.pax.pay.trans.task.PrintTask;
import com.pax.pay.utils.CurrencyConverter;
import com.pax.pay.utils.TransResultUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.view.dialog.CustomAlertDialog;
import com.pax.view.dialog.DialogUtils;

import java.math.BigDecimal;
import java.util.List;

public class SaleTrans extends BaseTrans {

    private byte searchCardMode = -1; // search card mode
    private String amount;
    private String tipAmount;
    private float percent; //adjust tips percent limit
    private TransData origTransData;
    private boolean firstIn = true;

    private boolean isFreePin;
    private boolean hasTip = false;
    private boolean needFallBack = false;
    private byte currentMode; //current search mode
    //处理强制走CUP的双标卡
    private boolean isForceCup;
    private static final String UNION_PAY = "UnionPay";
    private static final String HASE_CUP = "HASE_CUP";

    /**
     * @param context   :context
     * @param amount    :total amount
     * @param isFreePin :true free; false not
     * @param mode      {@link com.pax.pay.trans.action.ActionSearchCard.SearchMode}, 如果等于-1，
     */
    public SaleTrans(Context context, String amount, byte mode, boolean isFreePin,
                     TransEndListener transListener) {
        super(context, ETransType.SALE, transListener);
        setParam(amount, "0", mode, isFreePin, false);
    }

    /**
     * @param context   :context
     * @param isFreePin :true free; false not
     * @param mode      {@link com.pax.pay.trans.action.ActionSearchCard.SearchMode}, 如果等于-1，
     */
    public SaleTrans(Context context, Request requestData, boolean isFreePin, byte mode,
                     TransEndListener transListener) {
        super(context, ETransType.SALE, transListener);
        setParam(requestData.getAmountInCents(), "0", mode, isFreePin, false);
        isEcrMode = true;
        this.requestData = requestData;
        this.isForceCup = com.pax.pay.ecr.Constants.CUP_CARD_TYPE.equals(requestData.getCardType());
    }

    /**
     * @param context   :context
     * @param amount    :total amount
     * @param isFreePin :true free; false not
     * @param mode      {@link com.pax.pay.trans.action.ActionSearchCard.SearchMode}, 如果等于-1，
     */
    public SaleTrans(Context context, String amount, byte mode, boolean isFreePin,
                     boolean isForceCup) {
        super(context, ETransType.SALE, null);
        setParam(amount, "0", mode, isFreePin, false);
        this.isForceCup = isForceCup;
    }

    /**
     * @param request       the request from third party application
     * @param transListener transaction listener
     */
    public SaleTrans(Context context, BaseRequest request, TransEndListener transListener) {
        super(context, ETransType.SALE, transListener);
        thirdPartyBaseRequest = request;
        SaleRequest saleRequest = (SaleRequest) request;
        amount = String.valueOf(saleRequest.getAmount());
        if (0 != saleRequest.getTipAmount()) {
            hasTip = true;
            tipAmount = String.valueOf(saleRequest.getTipAmount());
        }
        isThirdMode = true;
        searchCardMode = Component.getCardReadMode(ETransType.SALE);
        isForceCup = saleRequest.getTransTypeDetail() == BaseRequest.TRANS_DETAIL_CUP;
    }

    /**
     *
     */
    private void setParam(String amount, String tipAmount, byte mode, boolean isFreePin,
                          boolean hasTip) {
        this.searchCardMode = mode;
        this.amount = amount;
        this.tipAmount = tipAmount;
        this.isFreePin = isFreePin;
        this.hasTip = hasTip;

        if (searchCardMode == -1) { // 待机银行卡消费入口
            searchCardMode = Component.getCardReadMode(ETransType.SALE);
            this.transType = ETransType.SALE;
        }
    }

    /**
     * bind state on action
     */
    @Override
    public void bindStateOnAction() {
        if (isEcrMode) {
            transData.setEcrMode(isEcrMode);
            transData.setEcrRefferenceNo(requestData.getEcrReferenceNo());
            transData.setEcrLoyaltyCode(requestData.getLoyaltyRequestCode());
            transData.setEcrMsgType(requestData.getMsgType());
            transData.setEcrOrigTransType(requestData.getMsgType());
            if (EcrConstants.CARD_PROMOTION_SALE == requestData.getMsgType()) {
                transData.setCardPromotion(requestData.getCardPromotion());
                if (requestData.getCardPromotion().equals("07")) {
                    transData.setOtherPromotionCardBin(requestData.getOtherPromotionCardBin());
                }
            }
        }

        if (isThirdMode) {
            int transTypeDetail = thirdPartyBaseRequest.getTransTypeDetail();
            transData.setEcrRefferenceNo(thirdPartyBaseRequest.getTransRefNo());
            transData.setTransTypeDetail(transTypeDetail);
            transData.setEcrMsgType((byte) thirdPartyBaseRequest.getTransType());
            transData.setEcrOrigTransType((byte) thirdPartyBaseRequest.getTransType());
            transData.setFromThirdParty(true);
        }

        if (isForceCup) {
            transData.setForceCup(true);
            Acquirer acquirer = FinancialApplication.getAcqManager().findAcquirer(HASE_CUP);
            transData.setAcquirer(acquirer);
        }

        if (amount != null && !amount.isEmpty()) {
            transData.setAmount(Long.parseLong(amount));
        }
        if (tipAmount != null && !tipAmount.isEmpty()) {
            transData.setTipAmount(Long.parseLong(tipAmount));
        }

        // enter trans amount action(This action is mainly used to handle bank card consumption and flash close paid deals)
        ActionEnterAmount amountAction = new ActionEnterAmount(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEnterAmount) action).setParam(getCurrentContext(),
                        getString(R.string.trans_sale), false);
            }
        });
        bind(State.ENTER_AMOUNT.toString(), amountAction, true);

        // search card action
        ActionSearchCard searchCardAction = new ActionSearchCard(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSearchCard) action).setParam(getCurrentContext(), getString(R.string.trans_sale),
                        searchCardMode,
                        String.valueOf(transData.getAmount()),
                        null, "", transData);
            }
        });

        bind(State.CHECK_CARD.toString(), searchCardAction, true);

        // enquiry action
        ActionTransOnline enquiryAction = new ActionTransOnline(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), getString(R.string.trans_sale),
                        transData);
            }
        });

        bind(State.ENQUIRY.toString(), enquiryAction);

        //select currency
        ActionSelectCurrency selectCurrencyAction = new ActionSelectCurrency(new ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionSelectCurrency) action).setParam(getCurrentContext(), getString(R.string.trans_sale),
                        String.valueOf(transData.getAmount()), transData.getRate(),
                        transData.getHomeCurrencyAmount(),
                        transData.getLocalCurrency(), transData.getHomeCurrency(),
                        FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer()), false, true,
                        transData.getDccMarkupRate());
            }
        });
        bind(State.SELECT_CURRENCY.toString(), selectCurrencyAction, true);

        //customer select currency
        ActionSelectCurrency customerSelectCurrencyAction =
                new ActionSelectCurrency(new ActionStartListener() {
                    @Override
                    public void onStart(AAction action) {
                        ((ActionSelectCurrency) action).setParam(getCurrentContext(),
                                getString(R.string.trans_sale),
                                String.valueOf(transData.getAmount()), transData.getRate(),
                                transData.getHomeCurrencyAmount(),
                                transData.getLocalCurrency(), transData.getHomeCurrency(),
                                FinancialApplication.getAcqManager().isVisaCard(transData.getIssuer()),
                                transData.getDccMarkupRate());
                    }
                });
        bind(State.CUSTOMER_SELECT_CURRENCY.toString(), customerSelectCurrencyAction, true);

        // Select the Cash Dollar action
        ActionCashDolSelect cashDolSelect = new ActionCashDolSelect(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionCashDolSelect) action).setParam(getCurrentContext(), transData);
            }
        });

        bind(State.SELECT_CASH_DOL.toString(), cashDolSelect, true);

        //input ref no
        ActionInputRefNo inputRefNoAction = new ActionInputRefNo(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionInputRefNo) action).setParam(getCurrentContext(),
                        getString(R.string.trans_sale),
                        getString(R.string.prompt_ref_no),
                        String.valueOf(transData.getAmount()));
            }
        });

        bind(State.ENTER_REF_NO.toString(), inputRefNoAction, true);

        //Scan QR Code
        ActionScanCode scanCodeAction = new ActionScanCode(null);
        bind(State.SCAN_CODE.toString(), scanCodeAction, true);

        //adjust tip action
        ActionAdjustTip adjustTipAction = new ActionAdjustTip(new ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                amount = String.valueOf(transData.getAmount() - transData.getTipAmount());
                ((ActionAdjustTip) action).setParam(getCurrentContext(),
                        getString(R.string.trans_sale),
                        amount, percent);
            }
        });
        bind(State.ADJUST_TIP.toString(), adjustTipAction, true);

        // enter pin action
        ActionEnterPin enterPinAction = new ActionEnterPin(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                //普通银行卡需要输入PIN,但HASE发行银行卡没有PIN
                boolean isMasterCard = transData.getIssuer().getName().equals("MASTER");
                // if flash pay by pwd,set isSupportBypass=false,need to enter pin
                ((ActionEnterPin) action).setParam(getCurrentContext(),
                        getString(R.string.trans_sale),
                        transData.getPan(), isFreePin,
                        getString(R.string.prompt_pin),
                        getString(R.string.prompt_no_pin),
                        transData.isDcc() ?
                                transData.getHomeCurrencyAmount() : String.valueOf(transData.getAmount()),
                        transData.isDcc() ?
                                transData.getOrigHomeCurrencyTipAmount()
                                : String.valueOf(transData.getTipAmount()),
                        transData.isDcc() ?
                                transData.getHomeCurrency().getCode()
                                : transData.getLocalCurrency().getCode(),
                        ActionEnterPin.EEnterPinType.ONLINE_PIN,
                        transData.isDcc(), isMasterCard, transData.getAcquirer());
            }
        });
        bind(State.ENTER_PIN.toString(), enterPinAction, true);

        // emv process action
        ActionEmvProcess emvProcessAction = new ActionEmvProcess(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionEmvProcess) action).setParam(getCurrentContext(), emv, transData);
            }
        });
        bind(State.EMV_PROC.toString(), emvProcessAction);

        //clss process action
        ActionClssProcess clssProcessAction = new ActionClssProcess(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionClssProcess) action).setParam(getCurrentContext(), clss, transData);
            }
        });
        bind(State.CLSS_PROC.toString(), clssProcessAction);

        //clss preprocess action
        ActionClssPreProc clssPreProcAction = new ActionClssPreProc(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionClssPreProc) action).setParam(clss, transData);
            }
        });
        bind(State.CLSS_PREPROC.toString(), clssPreProcAction);

        // online action
        ActionTransOnline transOnlineAction = new ActionTransOnline(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionTransOnline) action).setParam(getCurrentContext(), transData);
            }
        });

        bind(State.MAG_ONLINE.toString(), transOnlineAction, true);

        //enter auth code action
        ActionEnterAuthCode enterAuthCodeAction = new ActionEnterAuthCode(new AAction.ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionEnterAuthCode) action).setParam(getCurrentContext(),
                        getString(R.string.trans_sale),
                        getString(R.string.prompt_auth_code),
                        String.valueOf(transData.getAmount()), 6);
            }
        });
        bind(State.ENTER_AUTH_CODE.toString(), enterAuthCodeAction, true);

        // signature action
        ActionSignature signatureAction = new ActionSignature(new AAction.ActionStartListener() {

            @Override
            public void onStart(AAction action) {
                ((ActionSignature) action).setParam(getCurrentContext(),
                        transData.isDcc() ? transData.getHomeCurrencyAmount()
                                : String.valueOf(transData.getAmount()),
                        transData.isDcc() ? transData.getHomeCurrency().getCode()
                                : transData.getLocalCurrency().getCode(), getString(R.string.trans_sale));
            }
        });
        bind(State.SIGNATURE.toString(), signatureAction);

        //offline send
        ActionOfflineSend offlineSendAction = new ActionOfflineSend(new ActionStartListener() {
            @Override
            public void onStart(AAction action) {
                ((ActionOfflineSend) action).setParam(getCurrentContext(), transData);
            }
        });
        //even it failed to upload offline, it will continue current transaction, so the 3rd argv is false
        bind(State.OFFLINE_SEND.toString(), offlineSendAction);

        //print preview action
        PrintTask printTask = new PrintTask(getCurrentContext(), this,
                PrintTask.genTransEndListener(SaleTrans.this, State.PRINT.toString()));
        bind(State.PRINT.toString(), printTask);

        //如果ECR only=true, 则不允许从应用主界面发起交易,只能接受ECR交易请求
        if (!isEcrMode && Component.isEcrOnlyEnable()) {
            transEnd(new ActionResult(TransResult.ECR_MODE, null));
            return;
        }

        // perform the first action
        if (amount == null || amount.isEmpty()) {
            gotoState(State.ENTER_AMOUNT.toString());
        } else {
            gotoState(State.CLSS_PREPROC.toString());
        }
    }

    enum State {
        ENTER_AMOUNT,
        CHECK_CARD,
        /**
         * inquiry currency ratio
         */
        ENQUIRY,
        /**
         * select currency(local currency or foreign currency)
         */
        CUSTOMER_SELECT_CURRENCY,
        /**
         * select currency(local currency or foreign currency)
         */
        SELECT_CURRENCY,
        SELECT_CASH_DOL,
        SCAN_CODE,
        ADJUST_TIP,
        ENTER_PIN,
        MAG_ONLINE,
        EMV_PROC,
        CLSS_PREPROC,
        CLSS_PROC,
        SIGNATURE,
        ENTER_AUTH_CODE,
        OFFLINE_SEND,
        PRINT,
        ENTER_REF_NO,
    }

    /**
     * on action result
     *
     * @param currentState ：current State
     * @param result       ：current action result
     */
    @Override
    public void onActionResult(String currentState, ActionResult result) {
        int ret = result.getRet();
        LogUtils.e("SaleTrans", "onActionResult, currentState:" + currentState + ", result:" + ret);
        State state = State.valueOf(currentState);
        if (state == State.EMV_PROC) {
            // 不管emv处理结果成功还是失败，都更新一下冲正
            byte[] f55Dup = EmvTags.getF55(emv, transType, transData.isDcc(), true,
                    FinancialApplication.getAcqManager().getECardType(transData.getPan()));
            if (f55Dup.length > 0) {
                TransData dupTransData = FinancialApplication.getTransDataDbHelper()
                        .findFistDupRecordByAcquirer(transData.getAcquirer());
                if (dupTransData != null) {
                    dupTransData.setDupIccData(Utils.bcd2Str(f55Dup));
                    FinancialApplication.getTransDataDbHelper().updateTransData(dupTransData);
                }
            }
            if (ret == TransResult.NEED_FALL_BACK) {
                needFallBack = true;
                searchCardMode &= SearchMode.SWIPE;
                gotoState(State.CHECK_CARD.toString());
                return;
            } else if (ret != TransResult.SUCC) {
                transEnd(result);
                return;
            }
        }

        if (state == State.CLSS_PREPROC && ret != TransResult.SUCC) {
            searchCardMode &= 0x03;
        }

        if (state == State.CLSS_PROC && ret == TransResult.NEED_FALL_BACK) {
            needFallBack = true;
            searchCardMode &= SearchMode.SWIPE;
            gotoState(State.CHECK_CARD.toString());
            return;
        }

        switch (state) {
            case ENTER_AMOUNT:
                // save trans amount
                long enterAmount = (long) result.getData();
                //在Intended Offline模式下需要检查是否金额溢出
                if (FinancialApplication.getBaseManager().isIntendedOfflineMode()) {
                    long limitAmount = FinancialApplication.getSysParam().get(SysParam.NumberParam.INTENDED_OFFLINE_MONEY_LIMIE);
                    //如果没有值则默认使用1000.00元为上限值
                    limitAmount = (limitAmount == 0) ? 100000 : limitAmount;
                    Log.d(TAG, "Intended Offline Mode limitAmount :" + limitAmount);
                    if (enterAmount > limitAmount) {
                        transEnd(new ActionResult(TransResult.ERR_AMOUNT, null));
                        break;
                    }
                }
                transData.setAmount(enterAmount);
                gotoState(State.CLSS_PREPROC.toString());
                break;
            case CHECK_CARD: // subsequent processing of check card
                if (result.getRet() == TransResult.ERR_CARD_TYPE_MISMATCH) {
                    transEnd(result);
                } else {
                    onCheckCard(result);
                }
                break;
            case ENTER_REF_NO:
                onEnterRefNo(result);
                break;
            case ENQUIRY: // check currency ratio
                onEnquiry(ret);
                break;
            case SELECT_CURRENCY: // for customer to choose use Dcc or Dcd
                onSelectCurrency(result);
                break;
            case CUSTOMER_SELECT_CURRENCY:
                onCustomerSelectCurrency(result);
                break;
            case SELECT_CASH_DOL:
                goRefNOorTipBranch();
                break;
            case SCAN_CODE:
                afterScanCode(result);
                break;
            case ADJUST_TIP:
                onAdjustTip(result);
                break;
            case ENTER_PIN: // subsequent processing of enter pin
                onEnterPin(result);
                break;
            case MAG_ONLINE: // subsequent processing of online
                if (transData.getLysStatus() != null &&
                        !TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
                    DialogUtils.showWarnMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            displayLoyaltyStatus(transData.getLysStatus()),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    // determine whether need electronic signature or print
                                    checkOfflineTrans();
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    checkOfflineTrans();
                }
                break;
            case EMV_PROC: // emv后续处理
                //get trans result
                final CTransResult transResult = (CTransResult) result.getData();
                if (transData.getLysStatus() != null &&
                        !TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
                    DialogUtils.showWarnMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            displayLoyaltyStatus(transData.getLysStatus()),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    // EMV完整流程 脱机批准或联机批准都进入签名流程
                                    afterEMVProcess(transResult.getTransResult());
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    afterEMVProcess(transResult.getTransResult());
                }
                break;
            case CLSS_PREPROC:
                gotoState(State.CHECK_CARD.toString());
                break;
            case CLSS_PROC:
                final CTransResult clssResult = (CTransResult) result.getData();
                if (clssResult.getTransResult() == ETransResult.ERR_CARD_TYPE_MISMATCH) {
                    transEnd(new ActionResult(TransResult.ERR_CARD_TYPE_MISMATCH, null));
                    return;
                }
                if (transData.getLysStatus() != null &&
                        !TransData.LYSStatus.LYS_OK.equals(transData.getLysStatus())) {
                    DialogUtils.showWarnMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            displayLoyaltyStatus(transData.getLysStatus()),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    // determine whether need electronic signature or print
                                    afterClssProcess(clssResult);
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else if (FinancialApplication.getBaseManager().isIntendedOfflineMode()) {
                    //处理Intended Offline模式下拍卡之后的逻辑
                    //拍卡异常，重新设置检卡模式检卡
                    if (clssResult.getTransResult() == ETransResult.APPLICATION_REJECTION) {
                        searchCardMode &= 0x03;
                        gotoState(State.CHECK_CARD.toString());
                        return;
                    } else if (transData.getIssuer() != null && !"HASE_EMV".equals(transData.getAcquirer().getName())) {
                        //如果不是HASE_EMV收单行，则还是继续走EMV流程
                        afterClssProcess(clssResult);
                    } else {
                        //存入交易记录，如果插入失败则返回交易失败
                        if (transData.getEnterMode() == EnterMode.CLSS)
                            transData.setEnterMode(EnterMode.SWIPE);
                        transData.setTransType(ETransType.OFFLINE_TRANS_SEND);
                        transData.setOrigTransType(ETransType.OFFLINE_TRANS_SEND);
                        transData.setAuthCode(FinancialApplication.getSysParam().get(SysParam.StringParam.APPROVAL_CODE, null));
                        transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
                        transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
                        boolean isUpdateSuccess = FinancialApplication.getTransDataDbHelper().insertTransData(transData);
                        if (!isUpdateSuccess) {
                            transEnd(new ActionResult(TransResult.DATABASE_UPDATE_FAIL, null));
                        }
                        //increase trans no.
                        Component.incTransNo();
                        Component.incInvoiceNo();
                        gotoState(State.PRINT.toString());
                    }
                } else {
                    afterClssProcess(clssResult);
                }
                break;
            case OFFLINE_SEND: // send offline transaction
                toSignOrPrint();
                break;
            case ENTER_AUTH_CODE: // enter auth code
                onEnterAuthCode(result);
                break;
            case SIGNATURE:
                // save signature data
                byte[] signData = (byte[]) result.getData();
                byte[] signPath = (byte[]) result.getData1();

                if (signData != null && signData.length > 0 &&
                        signPath != null && signPath.length > 0) {
                    transData.setSignData(signData);
                    transData.setSignPath(signPath);
                    // update trans data，save signature
                    FinancialApplication.getTransDataDbHelper().updateTransData(transData);
                }
                gotoState(State.PRINT.toString());
                break;
            case PRINT:
                if (result.getRet() == TransResult.SUCC) {
                    ETransType transType = transData.getTransType();
                    if ((transType == ETransType.SALE ||
                            transType == ETransType.OFFLINE_TRANS_SEND) &&
                            FinancialApplication.getSysParam()
                                    .get(SysParam.BooleanParam.EDC_DCC_SALE_NEW_FLOW)
                            && firstIn
                            && FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_DCC)
                            && transData.getAcquirer().getName().equals("HASE_DCC")) {
                        gotoState(State.CUSTOMER_SELECT_CURRENCY.toString());
                    } else {
                        // end trans
                        transEnd(result);
                    }
                } else {
                    dispResult(transType.getTransName(), result, null);
                    gotoState(State.PRINT.toString());
                }
                break;
            default:
                transEnd(result);
                break;
        }
    }

    /**
     * on check card
     *
     * @param result ActionResult
     */
    private void onCheckCard(ActionResult result) {
        CardInformation cardInfo = (CardInformation) result.getData();
        saveCardInfo(cardInfo, transData);
        if (needFallBack && transData.isEcrMode() && transData.getCardPromotion() != null) {
            //fall back之后在search activity拿不到交易数据无法检查是否满足
            //cardPromotion的条件,因此这里在进行一次校验。
            checkCardPromotion();
            return;
        }
        if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP) &&
                FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_TIP_TYPE)) {
            transData.setIsPreSaleTips(true);
        }

        // enter card number manually
        currentMode = cardInfo.getSearchMode();
        if (currentMode == SearchMode.SWIPE || currentMode == SearchMode.KEYIN) {
            Acquirer acquirer = transData.getAcquirer();
            if (acquirer == null || acquirer.getName().equals("HASE_DCC") || acquirer.getName()
                    .equals("HASE_MACAO")) {
                acquirer = FinancialApplication.getAcqManager()
                        .findAcquirer(transData.getIssuer(), transData.getTransType());
                transData.setAcquirer(acquirer);
            }

            //如果是在IntendedOfflineMode下，恒生卡扫入HASE_EMV收单行
            if (FinancialApplication.getBaseManager().isIntendedOfflineMode() && acquirer != null && "HASE_OLS".equals(acquirer.getName())) {
                acquirer = FinancialApplication.getAcqManager().findAcquirer("HASE_EMV");
                transData.setAcquirer(acquirer);
            }

            if (acquirer != null && !Utils.checkAcquirerSettleStatus(acquirer.getName())) {
                transEnd(new ActionResult(TransResult.SETTLEMENT_PENDING, null));
                return;
            }
        }

        if (needFallBack) {
            transData.setEnterMode(EnterMode.FALLBACK);
        }

        if (currentMode == SearchMode.SWIPE || currentMode == SearchMode.KEYIN) {
            if (isEcrMode
                    && !TextUtils.isEmpty(transData.getEcrLoyaltyCode())
                    && !FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
                if (transData.getEcrLoyaltyCode().equals("03")) {
                    //ECR发起Cash Dollar交易，若卡片为恒生卡则直接进入redeem，否则强行结束交易
                    if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                        transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                        gotoState(State.SELECT_CASH_DOL.toString());
                    } else {
                        transEnd(new ActionResult(TransResult.ECR_NOT_HASE_CARD, null));
                    }
                } else if (transData.getEcrLoyaltyCode().equals("00")) {
                    // 当CUP_ECR开关关闭时，沿用旧模式，即ECR下的creadit交易不允许CUP间连交易
                    if (!FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_ENABLE_CUP_ECR) &&
                            transData.getIssuer().getName().equals(UNION_PAY)) {
                        transEnd(new ActionResult(TransResult.ERR_USER_CANCEL, null));
                    }
                    if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                        //如果ECR Redeem开关打开则进入Cash Dollar流程
                        if (FinancialApplication.getSysParam()
                                .get(SysParam.BooleanParam.EDC_ECR_ENABLE_REDEEM)) {
                            gotoState(State.SELECT_CASH_DOL.toString());
                        } else {
                            //如果ECR Redeem开关关闭则默认取消Cash Dollar
                            transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                            transData.setRedemptionFlag(
                                    RedeemConfig.getRedemptionFlag(RedeemConfig.REDEEM_NONE));
                            goRefNOorTipBranch();
                        }
                    } else {
                        //如果是强制走Cup则默认不进入Cash Dollar流程
                        goRefNOorTipBranch();
                    }
                }
            } else if (isThirdMode
                    && !(transData.getTransTypeDetail() == 0)
                    && !FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
                if (transData.getTransTypeDetail() == BaseRequest.TRANS_DETAIL_NORMAL_CASH_REDEEM) {
                    //三方发起Cash Dollar交易，若卡片为恒生卡则直接进入redeem，否则强行结束交易
                    if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                        transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                        gotoState(State.SELECT_CASH_DOL.toString());
                    } else {
                        transEnd(new ActionResult(TransResult.ECR_NOT_HASE_CARD, null));
                    }
                } else if (transData.getTransTypeDetail() == BaseRequest.TRANS_DETAIL_CREDIT_CARD) {
                    if (transData.getAcquirer().getName().equals("HASE_OLS")) {
                        //如果ECR Redeem开关打开则进入Cash Dollar流程
                        if (FinancialApplication.getSysParam()
                                .get(SysParam.BooleanParam.EDC_ECR_ENABLE_REDEEM)) {
                            gotoState(State.SELECT_CASH_DOL.toString());
                        } else {
                            //如果ECR Redeem开关关闭则默认取消Cash Dollar
                            transData.setTransType(ETransType.SALES_WITH_CASH_DOLLAR);
                            transData.setRedemptionFlag(
                                    RedeemConfig.getRedemptionFlag(RedeemConfig.REDEEM_NONE));
                            goRefNOorTipBranch();
                        }
                    } else {
                        //如果是强制走Cup则默认不进入Cash Dollar流程
                        goRefNOorTipBranch();
                    }
                } else if (transData.getTransTypeDetail() == BaseRequest.TRANS_DETAIL_CUP) {
                    goRefNOorTipBranch();
                }
            } else {
                //如果是强制走Cup 则不检查Cash Dollar
                if (transData.getAcquirer().getName().equals("HASE_OLS")
                        && !FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
                    gotoState(State.SELECT_CASH_DOL.toString());
                } else {
                    goRefNOorTipBranch();
                }
            }
        } else if (currentMode == SearchMode.INSERT) {
            needRemoveCard = true;
            // EMV process
            gotoState(State.EMV_PROC.toString());
        } else if (currentMode == SearchMode.WAVE) {
            needRemoveCard = true;
            // AET-15
            gotoState(State.CLSS_PROC.toString());
        } else if (currentMode == SearchMode.QR) {
            needRemoveCard = false;
            gotoState(State.SCAN_CODE.toString());
        }
    }

    private void onEnterRefNo(ActionResult result) {
        String refNo = (String) result.getData();
        transData.setAcqReferenceNo(refNo);

        goTipBranch();
    }

    private void checkCardPromotion() {
        String cardPromotion = transData.getCardPromotion();
        if ("07".equals(cardPromotion)) {
            List<String> promotionCardBins = Utils.splitPromotionCardBins(transData.getOtherPromotionCardBin());
            if (!Utils.isCardBinMatched(transData.getPan(), promotionCardBins)) {
                transEnd(new ActionResult(TransResult.ERR_CARD_TYPE_MISMATCH, null));
            }
        } else {
            if (!Utils.isCardPromotion(transData.getIssuer(), cardPromotion)) {
                transEnd(new ActionResult(TransResult.ERR_CARD_TYPE_MISMATCH, null));
            }
        }
    }

    private void goRefNOorTipBranch() {
        Acquirer acquirer = transData.getAcquirer();
        if (acquirer.isEnableRefNo()) {
            //enter ref no
            gotoState(State.ENTER_REF_NO.toString());
        } else {
            goTipBranch();
        }
    }

    /**
     * on adjust tip
     *
     * @param result ActionResult
     */
    private void onAdjustTip(ActionResult result) {
        transData.setIsPreSaleTips(true);
        //get total amount
        long totalAmountStr = CurrencyConverter.parse(result.getData().toString());
        transData.setAmount(totalAmountStr);
        //get tip amount
        long tip = CurrencyConverter.parse(result.getData1().toString());
        //set tip amount
        transData.setTipAmount(tip);
        if (currentMode == SearchMode.SWIPE || currentMode == SearchMode.KEYIN) {
            if (transData.getAcquirer().getName().equals(UNION_PAY) ||
                    transData.getAcquirer().getName().equals(HASE_CUP)) {
                gotoState(State.ENTER_PIN.toString());
            } else {
                // online process
                toEnquiryOrEnterPin();
            }
        }
    }

    /**
     * on enter pin
     *
     * @param result ActionResult
     */
    private void onEnterPin(ActionResult result) {
        String pinBlock = (String) result.getData();
        transData.setPin(pinBlock);
        if (pinBlock != null && !pinBlock.isEmpty()) {
            transData.setHasPin(true);
        }

        if (transData.getEnterMode() == EnterMode.SWIPE &&
                transData.getIssuer().getFloorLimit() > transData.getAmount()) {
            //現HASE所有交易, 除了offline 交易, 都必需要聯機交易.所以,這個offline流程不会用到
            // save trans data
            transData.setTransType(ETransType.OFFLINE_TRANS_SEND);
            transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
            transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
            FinancialApplication.getTransDataDbHelper().insertTransData(transData);
            //increase trans no.
            Component.incTransNo();
            toSignOrPrint();
            return;
        }

        //clss process
        if (transData.getEnterMode() == EnterMode.CLSS &&
                transData.getEmvResult() == ETransResult.CLSS_OC_APPROVED) {
            if (!transData.isSignFree()) {
                checkOfflineTrans();
            } else {
                gotoState(State.PRINT.toString());
            }
            return;
        }

        // online process
        gotoState(State.MAG_ONLINE.toString());
    }

    /**
     * check offline trans
     */
    private void checkOfflineTrans() {
        if ("HASE_OLS".equals(transData.getAcquirer().getName())) {
            toSignOrPrint();
            return;
        }
        boolean enableReferral =
                FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_REFERRAL);
        boolean isAmex = Component.isAmex(transData.getIssuer().getName());
        if (transData.getResponseCode().equals("02") && enableReferral && isAmex) {
            gotoState(State.ENTER_AUTH_CODE.toString());
        } else {
            gotoState(State.OFFLINE_SEND.toString());
        }
    }

    /**
     * go tip branch
     */
    private void goTipBranch() {
        boolean enableTip = FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_TIP) &&
                FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_TIP_TYPE);
        if (transData.getAcquirer()
                .getName()
                .equals(HASE_CUP)) { //Issue 94 Disable the CUP tips as HASE host not yet support
            enableTip = false;
        }
        //adjust tip
        long totalAmount = transData.getAmount();
        tipAmount = String.valueOf(transData.getTipAmount());
        long lTipAmountLong = Utils.parseLongSafe(tipAmount, 0);
        long baseAmount = totalAmount - lTipAmountLong;
        percent = transData.getIssuer().getAdjustPercent();

        if (enableTip) {
            if (!hasTip) {
                gotoState(State.ADJUST_TIP.toString());
            } else if (baseAmount * percent / 100 < lTipAmountLong) {
                showAdjustTipDialog(getCurrentContext());
            } else {
                if (transData.getAcquirer().getName().equals(UNION_PAY) ||
                        transData.getAcquirer().getName().equals(HASE_CUP)) {
                    gotoState(State.ENTER_PIN.toString());
                } else {
                    // online process
                    toEnquiryOrEnterPin();
                }
            }
        } else {
            if (transData.getAcquirer().getName().equals(UNION_PAY) ||
                    transData.getAcquirer().getName().equals(HASE_CUP)) {
                gotoState(State.ENTER_PIN.toString());
            } else {
                // online process
                toEnquiryOrEnterPin();
            }
        }
    }

    /**
     * check the card is local card or not, and terminal support dcc or not
     */
    private void toEnquiryOrEnterPin() {
        if (FinancialApplication.getBaseManager().checkDccAllowed(transData) && !FinancialApplication.getBaseManager().isIntendedOfflineMode()) {
            transData.setOrigAcquirer(transData.getAcquirer());
            //check card bin
            int ret = Component.checkCardBin(transData.getPan());
            //local card bin can not do dcc
            if (ret < 0) {
                if (ret == TransResult.ERR_NO_CARD_BIN) {
                    DialogUtils.showErrMessage(getCurrentContext(), transData.getTransType().getTransName(),
                            TransResultUtils.getMessage(TransResult.ERR_NO_CARD_BIN),
                            new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialogInterface) {
                                    toDoDcc();
                                }
                            }, Constants.FAILED_DIALOG_SHOW_TIME);
                } else {
                    transEnd(new ActionResult(ret, null));
                }
                return;
            } else if (ret == 0) {
                //for Macao transfer to PP host and change trans data setting
                if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
                    Component.macaoTransSetting(transData);
                } else {
                    //go DCD
                    Component.transUpdate(transData);
                }
                gotoState(State.MAG_ONLINE.toString());
                firstIn = false;
                return;
            }
            //go DCC
            toDoDcc();
        } else {
            //for Macao transfer to PP host and change trans data setting
            if (FinancialApplication.getBaseManager().isMacaoLocalTrans(transData)) {
                Component.macaoTransSetting(transData);
            }
            //刷卡或者手输之后，如果判断是Intended Offline Mode，则直接存储数据，结束交易
            if (FinancialApplication.getBaseManager().isIntendedOfflineMode() && "HASE_EMV".equals(transData.getAcquirer().getName())) {
                transData.setTransType(ETransType.OFFLINE_TRANS_SEND);
                transData.setOrigTransType(ETransType.OFFLINE_TRANS_SEND);
                transData.setAuthCode(FinancialApplication.getSysParam().get(SysParam.StringParam.APPROVAL_CODE, null));
                transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
                transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
                boolean isUpdateSuccess = FinancialApplication.getTransDataDbHelper().insertTransData(transData);
                if (!isUpdateSuccess) {
                    transEnd(new ActionResult(TransResult.DATABASE_UPDATE_FAIL, null));
                }
                //increase trans no.
                Component.incTransNo();
                Component.incInvoiceNo();
                gotoState(State.PRINT.toString());
            } else {
                // enter pin or not
                gotoState(State.MAG_ONLINE.toString());
            }
        }
    }

    /**
     * go dcc process, check dcc ratio
     */
    private void toDoDcc() {
        //enquiry rate
        Component.dccTransInit(transData);
        transData.setTransType(ETransType.ENQUIRY);
        gotoState(State.ENQUIRY.toString());
    }

    private void onEnquiry(int ret) {
        //enquiry failed, run dcd
        transData.setTransType(ETransType.SALE);
        if (ret != TransResult.SUCC) {
            if (ret < 0) {
                transEnd(new ActionResult(ret, null));
                return;
            }
            //for Macao transfer to PP host and change trans data setting
            if (FinancialApplication.getBaseManager().isFullOutsorceMode(transData)) {
                Component.macaoTransSetting(transData);
            } else {
                //go DCD
                Component.transUpdate(transData);
            }
            gotoState(State.MAG_ONLINE.toString());
            return;
        }

        if (!(isEcr && FinancialApplication.getSysParam()
                .get(SysParam.BooleanParam.EDC_SUPPORT_ECR_CHOOSE_DCC))) {
            gotoState(State.SELECT_CURRENCY.toString());
            return;
        }

        //ecr
        if (transListener == null) {
            transEnd(new ActionResult(TransResult.ERR_ECR, null));
            return;
        }
        transListener.onEnd(new ActionResult(TransResult.SUCC_ECR_SELECT_CURRENCY, transData, dccListener));
        waitForEcrSelect(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                if (ecrResult != 0) {
                    //ecr err
                    transEnd(new ActionResult(TransResult.ERR_ECR, null));
                    return;
                }
                //after selecting currency, to enter pin
                gotoState(State.MAG_ONLINE.toString());
            }
        });
    }

    /**
     * check customer select dcc or local currency to pay
     */
    private void onSelectCurrency(ActionResult result) {
        // get whether customer select local currency or dcc
        String temp = (String) result.getData();
        if (temp.equals(SelectCurrencyActivity.LOCAL_CURRENCY)) { // DCD transaction
            transData.setIsFullOutSource(FinancialApplication.getBaseManager().isFullOutsorceMode(transData));
            if (transData.isFullOutSource()) {
                FinancialApplication.getBaseManager().setMacaoLocalEmvData(transData);
                Component.macaoAcqUpdate(transData);
            }
            Component.transUpdate(transData);
        } else { //DCC currency
            transData.setHomeCurrencyTipAmount(Component.localAmountToHomeAmount(transData.getHomeCurrency(),
                    String.valueOf(transData.getTipAmount()), transData.getRate()));
        }
        gotoState(State.MAG_ONLINE.toString());
    }

    /**
     * for sale one step flow only, adjust dcc trans' amount to 0 and do offline trans on DCD
     */
    private void onCustomerSelectCurrency(ActionResult result) {
        //get customer choose which currency
        String temp = (String) result.getData();
        if (temp.equals(SelectCurrencyActivity.LOCAL_CURRENCY)) { // DCD transaction
            firstIn = false;
            origTransData = FinancialApplication.getTransDataDbHelper()
                    .findTransDataByInvoiceNo(transData.getInvoiceNo());
            long maxValue = 99999999999999L;
            long origTotalAmount = origTransData.getAmount();
            long origTipAmount = origTransData.getTipAmount();
            long baseAmount = origTotalAmount - origTipAmount;
            long newTipAmount;
            if (baseAmount > maxValue) {
                transEnd(new ActionResult(TransResult.ERR_AMOUNT, null));
                return;
            }
            //get new tip amount
            newTipAmount = 0;

            BigDecimal hundredBd = new BigDecimal(100);
            BigDecimal percentBd = BigDecimal.valueOf(origTransData.getIssuer().getAdjustPercent());
            BigDecimal baseAmountBd = new BigDecimal(baseAmount);
            BigDecimal maxTipsBd = baseAmountBd.multiply(percentBd).divide(hundredBd, 2);
            BigDecimal tipAmountBd = new BigDecimal(newTipAmount);

            //check tip amount
            if (maxTipsBd.doubleValue() < tipAmountBd.doubleValue()) {
                transEnd(new ActionResult(TransResult.ERR_AMOUNT, null));
                return;
            }
            transSuccess(baseAmount, newTipAmount);
        } else { // DCC transaction
            firstIn = false;
            transData.setSignFree(true);
            transData.setCustomerSelectDCC(true);
            gotoState(State.PRINT.toString());
        }
    }

    /**
     * After trans success update trans data
     */
    private void transSuccess(long newTotalAmount, long newTipAmount) {
        Component.transSuccess(newTotalAmount, newTipAmount, origTransData, transData, true);

        //error checking
        if (transData.getAcquirer() == null) {
            transEnd(new ActionResult(TransResult.ERR_NO_MATCH_ACQUIRER, null));
            return;
        }

        if (Component.chkSaleRRNNoExist(transData)) {
            transEnd(new ActionResult(TransResult.ERR_UNPACK, null));
            return;
        }

        //insert successful trans data
        boolean isInsertSuccess = FinancialApplication.getTransDataDbHelper().insertTransData(transData);
        if (!isInsertSuccess) {
            transEnd(new ActionResult(TransResult.ERR_DATABASE_OPERATE, null));
            return;
        }

        Component.origTransDataInit(origTransData);
        gotoState(State.PRINT.toString());
    }

    /**
     * need electronic signature or send
     */
    private void toSignOrPrint() {
        if (FinancialApplication.getSysParam()
                .get(SysParam.BooleanParam.EDC_ENABLE_SALE_TRANS_NO_SIGN)
                && currentMode != SearchMode.SWIPE
                && currentMode != SearchMode.KEYIN) {
            transData.setSignFree(true);
            gotoState(State.PRINT.toString());
        } else {
            if (Component.isSignatureFree(transData)) {// signature free
                // print preview
                transData.setSignFree(true);
                gotoState(State.PRINT.toString());
            } else {
                transData.setSignFree(false);
                gotoState(State.PRINT.toString());//恒生要求移除Signature流程，但是数据库中的signFree需保留置位
            }
        }
        FinancialApplication.getTransDataDbHelper().updateTransData(transData);
    }

    /**
     * after emv process
     *
     * @param transResult ETransResult
     */
    private void afterEMVProcess(ETransResult transResult) {
        EmvTransProcess.emvTransResultProcess(transResult, emv, transData);
        if (transResult == ETransResult.ONLINE_APPROVED) {// 联机批准
            checkOfflineTrans();
        } else if (transResult == ETransResult.OFFLINE_APPROVED) {//脱机批准处理
            //恒生不支持脱机批准, 因为已经有了Offline Sale
            transEnd(new ActionResult(TransResult.ERR_NOT_SUPPORT_TRANS, null));
        } else {
            boolean enableReferral =
                    FinancialApplication.getSysParam().get(SysParam.BooleanParam.EDC_SUPPORT_REFERRAL);
            boolean isAmex = Component.isAmex(transData.getIssuer().getName());
            if (isAmex
                    && "02".equals(transData.getResponseCode())
                    && enableReferral) { //For amex support referral
                gotoState(State.ENTER_AUTH_CODE.toString());
            } else {
                transactionFailProcess(transResult);
            }
        }
    }

    /**
     * after clss process
     *
     * @param transResult CTransResult
     */
    private void afterClssProcess(CTransResult transResult) {
        // 设置交易结果
        transData.setEmvResult(transResult.getTransResult());
        //Tap card fails will fall to swipe / insert card mode
        if (transResult.getTransResult() == ETransResult.APPLICATION_REJECTION) {
            searchCardMode &= 0x03;
            gotoState(State.CHECK_CARD.toString());
            return;
        }
        ClssTransProcess.clssTransResultProcess(transResult, clss, transData);

        if (transResult.getCvmResult() == ECvmResult.SIG
                || transResult.getCvmResult()
                == ECvmResult.ONLINE_PIN_SIG /*|| !Component.isSignatureFree(transData)*/) { // AET-283     //Jerry mask
            //FIXME don't upload the signature
            //do signature after online
            transData.setSignFree(false);
        } else {
            transData.setSignFree(true);
        }

        if (transResult.getTransResult() == ETransResult.CLSS_OC_SEE_PHONE) {
            //
            gotoState(State.CLSS_PREPROC.toString());
        } else if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_AGAIN) {
            DialogUtils.showErrMessage(getCurrentContext(), transType.getTransName(),
                    getString(R.string.prompt_please_retry), null, Constants.FAILED_DIALOG_SHOW_TIME);
            //AET-175
            gotoState(State.CLSS_PREPROC.toString());
        } else if (transResult.getTransResult() == ETransResult.CLSS_OC_TRY_ANOTHER_INTERFACE) {
            //此处应当结束整个交易，重新发起交易时，客户自然会使用接触式
            transEnd(new ActionResult(TransResult.PLEASE_USE_CONTACT, null));
        } else if (transResult.getTransResult() == ETransResult.CLSS_OC_APPROVED
                || transResult.getTransResult() == ETransResult.ONLINE_APPROVED) {
            transData.setOnlineTrans(transResult.getTransResult() == ETransResult.ONLINE_APPROVED);
            checkOfflineTrans();
        } else if (transResult.getTransResult() == ETransResult.OFFLINE_APPROVED) {//脱机批准处理
            //恒生不支持脱机批准, 因为已经有了Offline Sale
            transEnd(new ActionResult(TransResult.ERR_NOT_SUPPORT_TRANS, null));
        } else if (transResult.getTransResult() == ETransResult.NOT_HASE_CARD) {
            transEnd(new ActionResult(TransResult.ECR_NOT_HASE_CARD, null));
        } else if (transResult.getTransResult() == ETransResult.TRANS_NOT_ACCEPT) {
            transEnd(new ActionResult(TransResult.TRANS_NOT_ACCEPT, null));
        } else {
            boolean enableReferral =
                    FinancialApplication.getSysParam()
                            .get(SysParam.BooleanParam.EDC_SUPPORT_REFERRAL);
            boolean isAmex = Component.isAmex(transData.getIssuer().getName());
            if (isAmex
                    && "02".equals(transData.getResponseCode())
                    && enableReferral) { //For amex support referral
                gotoState(State.ENTER_AUTH_CODE.toString());
            } else {
                transactionFailProcess(transResult.getTransResult());
            }
        }
    }

    /**
     * show adjust tip dialog
     *
     * @param context Context
     */
    private void showAdjustTipDialog(final Context context) {
        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);
        dialog.setCancelClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                dialog.dismiss();
                transEnd(new ActionResult(TransResult.ERR_ABORTED, null));
            }
        });
        dialog.setConfirmClickListener(new CustomAlertDialog.OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                dialog.dismiss();
                gotoState(State.ADJUST_TIP.toString());
            }
        });
        dialog.show();
        dialog.setNormalText(getString(R.string.prompt_tip_exceed));
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
    }

    /**
     * after scan code
     *
     * @param result ActionResult
     */
    private void afterScanCode(ActionResult result) {
        String qrCode = (String) result.getData();
        if (TextUtils.isEmpty(qrCode)) {
            transEnd(new ActionResult(TransResult.ERR_INVALID_EMV_QR, null));
            return;
        }

        int ret = EmvQr.decodeEmvQr(transData, qrCode);
        if (ret != 0) {
            transEnd(new ActionResult(ret, null));
            return;
        }

        ret = Component.checkQRCode(transData);
        if (ret != TransResult.SUCC) {
            transEnd(new ActionResult(ret, null));
            return;
        }
        gotoState(State.MAG_ONLINE.toString());
    }

    /**
     * on enter auth code
     *
     * @param result ActionResult
     */
    private void onEnterAuthCode(ActionResult result) {
        //get auth code
        String authCode = (String) result.getData();

        //set auth code
        transData.setAuthCode(authCode);

        // save trans data
        transData.setOnlineTrans(false);
        // transData.setRefNo("");
        transData.setOfflineSendState(TransData.OfflineStatus.OFFLINE_NOT_SENT);
        transData.setReversalStatus(TransData.ReversalStatus.NORMAL);
        transData.setBelowFloorLimitTerminalApproved(true);
        FinancialApplication.getTransDataDbHelper().insertTransData(transData);

        // increase trans no.
        Component.incTransNo();

        //sign and print
        toSignOrPrint();
    }
}
