/*
 * ===========================================================================================
 *   = COPYRIGHT
 *            PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *     This software is supplied under the terms of a license agreement or nondisclosure
 *     agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *     disclosed except in accordance with the terms in that agreement.
 *       Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *   Description: // Detail description about the function of this module,
 *               // interfaces with the other modules, and dependencies.
 *   Revision History:
 *   Date	                 Author	                Action
 *   20220809  	             yangrr           	    Create/Add/Modify/Delete
 *  ===========================================================================================
 */

package com.pax.pay.ecr.responses;

import com.pax.abl.core.ActionResult;
import com.pax.ecrsdk.message.request.Request;
import com.pax.ecrsdk.message.response.CardPromoptionSaleResponse;
import com.pax.ecrsdk.message.response.Response;
import com.pax.pay.trans.component.Component;

import java.util.Objects;

public class CardPromotionSaleResponseBuilder extends BaseResponseBuilder {
    @Override
    public Response build(Request reqMsg, ActionResult result) {
        CardPromoptionSaleResponse cardPromoptionSaleResponse = new CardPromoptionSaleResponse();
        buildRspData(cardPromoptionSaleResponse, reqMsg, result);
        return cardPromoptionSaleResponse;
    }

    @Override
    public void buildRspData(Response rspMsg, Request reqMsg, ActionResult result) {
        CardPromoptionSaleResponse response = (CardPromoptionSaleResponse) rspMsg;
        super.buildRspData(response, reqMsg, result);

        //Retrieval 以 ECR 進行的交易或 Standalone 進行的交易设置MsgType和OriTransType
        if (isRetrievalTrans(reqMsg.getMsgType())) {
            setEcrRetrievalType(response, reqMsg);
        } else {
            response.setOriTransType(
                    Objects.isNull(transData) ? 0x20 : transData.getEcrOrigTransType());
        }

        response.setAmountInCents(
                Component.getPaddedString(
                        String.valueOf(Objects.isNull(transData) ? 0 : transData.getAmount()), 12,
                        '0'));

        //沒有 tips 的話, 默認於 '0' (即是 0x30), 所以這個域會默認為 "000000000000".
        response.setTipInCents(Component.getPaddedString("0", 12, '0'));

        response.setCardNo(getCardNo());
        response.setExpireDate(getExpDate());

        response.setTraceNo(
                Component.getPaddedNumber(Objects.isNull(transData) ? 0 : transData.getInvoiceNo(),
                        6));

        response.setBatchNo(
                Component.getPaddedNumber(Objects.isNull(transData) ? 0 : transData.getBatchNo(),
                        6));

        response.setApprovalCode(getApprovalCode());
        response.setRetrievalRefNo(getRRN());

        response.setCurrencyCode(getCurrencyCode());
        response.setExchangeRate(getExchangeRate());
        response.setAmtInForeignCurrency(getAmtInForeignCurrency());
        response.setEntryMode(getPosEntryMode());
    }

    private String getCurrencyCode() {
        String currencyCode = Component.getPaddedString(" ", 3, ' ');
        if (transData != null && transData.isDcc() && !transData.isFullOutSource()) {
            currencyCode =
                    String.format("%3s", Long.parseLong(transData.getHomeCurrency().getCode()));
        }
        return currencyCode;
    }

    private String getExchangeRate() {
        String rate = Component.getPaddedString(" ", 8, ' ');
        ;
        if (transData != null && transData.isDcc() && !transData.isFullOutSource()) {
            rate = String.format("%8s", Long.parseLong(transData.getRate()));
        }
        return rate;
    }

    private String getAmtInForeignCurrency() {
        String foreignCurrency = Component.getPaddedString(" ", 12, ' ');
        if (transData != null && transData.isDcc() && !transData.isFullOutSource()) {
            foreignCurrency =
                    String.format("%012d", Long.parseLong(transData.getHomeCurrencyAmount()));
        }
        return foreignCurrency;
    }
}
